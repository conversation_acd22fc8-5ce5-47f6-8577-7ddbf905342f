import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link, useNavigate } from 'react-router-dom';

const API_KEY = import.meta.env.VITE_TMDB_API_KEY;

// Series card component
const SeriesCard = ({ series }) => {
  const navigate = useNavigate();

  const handlePlayClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    const params = new URLSearchParams({
      type: 'tv',
      title: series.name,
      season: '1',
      episode: '1'
    });

    navigate(`/play/${series.id}?${params.toString()}`);
  };

  return (
    <div className="movie-card fade-in-up">
      <Link to={`/series/${series.id}`}>
        <div className="movie-poster-container">
          <img
            className="movie-poster"
            src={series.poster_path
              ? `https://image.tmdb.org/t/p/w500${series.poster_path}`
              : '/api/placeholder/300/450'
            }
            alt={series.name}
            loading="lazy"
          />
          <div className="movie-overlay">
            <button
              className="play-button"
              onClick={handlePlayClick}
              aria-label={`Watch ${series.name}`}
            >
              <svg className="play-icon" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </button>
          </div>
        </div>
        <div className="movie-info">
          <h3 className="movie-title">{series.name}</h3>
          <div className="movie-meta">
            <span className="movie-year">
              {new Date(series.first_air_date).getFullYear() || 'N/A'}
            </span>
            <div className="movie-rating">
              <svg className="star-icon" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span>{series.vote_average?.toFixed(1) || 'N/A'}</span>
            </div>
          </div>
          {series.genre_ids && series.genre_ids.length > 0 && (
            <div className="movie-genres">
              {series.genre_ids.slice(0, 2).map((genreId) => (
                <span key={genreId} className="genre-tag">
                  {getGenreName(genreId)}
                </span>
              ))}
            </div>
          )}
        </div>
      </Link>
    </div>
  );
};

// Loading skeleton component
const SkeletonCard = () => (
  <div className="skeleton-card">
    <div className="skeleton skeleton-poster"></div>
    <div className="skeleton-info">
      <div className="skeleton skeleton-title"></div>
      <div className="skeleton skeleton-meta"></div>
    </div>
  </div>
);

// TV Genre mapping
const tvGenres = {
  10759: 'Action & Adventure', 16: 'Animation', 35: 'Comedy', 80: 'Crime',
  99: 'Documentary', 18: 'Drama', 10751: 'Family', 10762: 'Kids',
  9648: 'Mystery', 10763: 'News', 10764: 'Reality', 10765: 'Sci-Fi & Fantasy',
  10766: 'Soap', 10767: 'Talk', 10768: 'War & Politics', 37: 'Western'
};

const getGenreName = (genreId) => tvGenres[genreId] || 'Unknown';

function Series() {
  const [series, setSeries] = useState([]);
  const [genres, setGenres] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState('popular');
  const [selectedGenre, setSelectedGenre] = useState('');
  const [sortBy, setSortBy] = useState('popularity.desc');

  const categories = [
    { value: 'popular', label: '🔥 Popular', endpoint: 'popular' },
    { value: 'top_rated', label: '⭐ Top Rated', endpoint: 'top_rated' },
    { value: 'airing_today', label: '📺 Airing Today', endpoint: 'airing_today' },
    { value: 'on_the_air', label: '🎭 On The Air', endpoint: 'on_the_air' }
  ];

  const sortOptions = [
    { value: 'popularity.desc', label: 'Most Popular' },
    { value: 'popularity.asc', label: 'Least Popular' },
    { value: 'vote_average.desc', label: 'Highest Rated' },
    { value: 'vote_average.asc', label: 'Lowest Rated' },
    { value: 'first_air_date.desc', label: 'Newest First' },
    { value: 'first_air_date.asc', label: 'Oldest First' },
    { value: 'name.asc', label: 'A-Z' },
    { value: 'name.desc', label: 'Z-A' }
  ];

  useEffect(() => {
    fetchGenres();
  }, []);

  const fetchGenres = async () => {
    try {
      const response = await axios.get(
        `https://api.themoviedb.org/3/genre/tv/list?api_key=${API_KEY}`
      );
      setGenres(response.data.genres);
    } catch (error) {
      console.error('Error fetching genres:', error);
    }
  };

  useEffect(() => {
    const fetchSeries = async () => {
      try {
        setLoading(true);
        setError(null);

        let url;
        if (selectedGenre) {
          // Use discover endpoint for genre filtering
          url = `https://api.themoviedb.org/3/discover/tv?api_key=${API_KEY}&with_genres=${selectedGenre}&sort_by=${sortBy}&page=${currentPage}`;
        } else {
          // Use category endpoints
          const category = categories.find(cat => cat.value === selectedCategory);
          url = `https://api.themoviedb.org/3/tv/${category.endpoint}?api_key=${API_KEY}&page=${currentPage}`;
        }

        const response = await axios.get(url);
        setSeries(response.data.results);
        setTotalPages(Math.min(response.data.total_pages, 500)); // TMDB limits to 500 pages
      } catch (error) {
        console.error('Error fetching series:', error);
        setError('Failed to load TV series. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchSeries();
  }, [selectedCategory, selectedGenre, sortBy, currentPage]);

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    setSelectedGenre('');
    setCurrentPage(1);
  };

  const handleGenreChange = (genreId) => {
    setSelectedGenre(genreId);
    setSelectedCategory('');
    setCurrentPage(1);
  };

  const handleSortChange = (sort) => {
    setSortBy(sort);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  if (error) {
    return (
      <div className="container">
        <div className="error-message text-center">
          <h2>Oops! Something went wrong</h2>
          <p>{error}</p>
          <button
            className="btn btn-primary mt-2"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container fade-in">
      <div className="movies-header">
        <h1>📺 TV Series</h1>
        <p className="movies-subtitle">Discover amazing TV shows and series from around the world</p>
      </div>

      {/* Filters and Controls */}
      <div className="filters-container">
        {/* Category Filters */}
        <div className="filter-group">
          <h3 className="filter-title">Categories</h3>
          <div className="filter-buttons">
            {categories.map((category) => (
              <button
                key={category.value}
                className={`filter-btn ${selectedCategory === category.value ? 'active' : ''}`}
                onClick={() => handleCategoryChange(category.value)}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Genre Filters */}
        <div className="filter-group">
          <h3 className="filter-title">Genres</h3>
          <div className="filter-buttons">
            <button
              className={`filter-btn ${!selectedGenre ? 'active' : ''}`}
              onClick={() => handleGenreChange('')}
            >
              All Genres
            </button>
            {genres.slice(0, 8).map((genre) => (
              <button
                key={genre.id}
                className={`filter-btn ${selectedGenre === genre.id.toString() ? 'active' : ''}`}
                onClick={() => handleGenreChange(genre.id.toString())}
              >
                {genre.name}
              </button>
            ))}
          </div>
        </div>

        {/* Sort Options */}
        {(selectedGenre || selectedCategory === '') && (
          <div className="filter-group">
            <h3 className="filter-title">Sort By</h3>
            <select
              className="sort-select"
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* Series Grid */}
      {loading ? (
        <div className="movie-grid">
          {Array(20).fill().map((_, index) => (
            <SkeletonCard key={index} />
          ))}
        </div>
      ) : series.length > 0 ? (
        <>
          <div className="movie-grid">
            {series.map((show) => (
              <SeriesCard key={show.id} series={show} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination">
              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </button>

              <div className="pagination-info">
                Page {currentPage} of {totalPages}
              </div>

              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="no-results">
          <div className="no-results-content">
            <h2>No TV series found</h2>
            <p>Try adjusting your filters or search criteria.</p>
            <button
              className="btn btn-primary"
              onClick={() => {
                setSelectedCategory('popular');
                setSelectedGenre('');
                setSortBy('popularity.desc');
                setCurrentPage(1);
              }}
            >
              Reset Filters
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Series;