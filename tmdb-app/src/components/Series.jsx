import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';

const API_KEY = '3308647fabe47a844ab269e6eab19132'; // Replace with your actual API key

function Series() {
  const [popularSeries, setPopularSeries] = useState([]);
  const [topRatedSeries, setTopRatedSeries] = useState([]);
  const [airingTodaySeries, setAiringTodaySeries] = useState([]);

  useEffect(() => {
    async function fetchData() {
      try {
        const popularSeriesResponse = await axios.get(
          `https://api.themoviedb.org/3/tv/popular?api_key=${API_KEY}`
        );
        setPopularSeries(popularSeriesResponse.data.results);

        const topRatedSeriesResponse = await axios.get(
          `https://api.themoviedb.org/3/tv/top_rated?api_key=${API_KEY}`
        );
        setTopRatedSeries(topRatedSeriesResponse.data.results);

        const airingTodaySeriesResponse = await axios.get(
          `https://api.themoviedb.org/3/tv/airing_today?api_key=${API_KEY}`
        );
        setAiringTodaySeries(airingTodaySeriesResponse.data.results);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    }
    fetchData();
  }, []);

  return (
    <div>
      <h1>Series</h1>

      <h2>Popular Series</h2>
      <div className="movie-list">
        {popularSeries.map((series) => (
          <div className="movie-card" key={series.id}>
            <Link to={`/movie/${series.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${series.poster_path}`}
                alt={series.name}
              />
              <p>{series.name}</p>
            </Link>
          </div>
        ))}
      </div>

      <h2>Top Rated Series</h2>
      <div className="movie-list">
        {topRatedSeries.map((series) => (
          <div className="movie-card" key={series.id}>
            <Link to={`/movie/${series.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${series.poster_path}`}
                alt={series.name}
              />
              <p>{series.name}</p>
            </Link>
          </div>
        ))}
      </div>

      <h2>Airing Today Series</h2>
      <div className="movie-list">
        {airingTodaySeries.map((series) => (
          <div className="movie-card" key={series.id}>
            <Link to={`/movie/${series.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${series.poster_path}`}
                alt={series.name}
              />
              <p>{series.name}</p>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Series;