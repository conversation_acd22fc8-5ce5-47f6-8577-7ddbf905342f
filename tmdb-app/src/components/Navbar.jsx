import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

function Navbar() {
  const [searchQuery, setSearchQuery] = useState('');
  const location = useLocation();

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    // Implement search functionality here
    console.log('Search query:', searchQuery);
  };

  const getNavLinkStyle = (path) => {
    return {
      color: '#fff',
      textDecoration: 'none',
      marginRight: '20px',
      borderBottom: location.pathname === path ? '2px solid #e50914' : 'none',
      paddingBottom: '5px',
    };
  };

  return (
    <nav style={{ backgroundColor: '#141414', padding: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
      <Link to="/" style={{ color: '#e50914', textDecoration: 'none', fontSize: '24px', fontWeight: 'bold' }}>TMDB</Link>
      <div>
        <Link to="/" style={getNavLinkStyle('/')}>Home</Link>
        <Link to="/movies" style={getNavLinkStyle('/movies')}>Movies</Link>
        <Link to="/series" style={getNavLinkStyle('/series')}>Series</Link>
      </div>
      <form onSubmit={handleSearchSubmit}>
        <input
          type="text"
          placeholder="Search"
          value={searchQuery}
          onChange={handleSearchChange}
          style={{ padding: '10px', borderRadius: '5px', border: 'none', backgroundColor: '#333', color: '#fff' }}
        />
        <button type="submit" style={{ padding: '10px', borderRadius: '5px', border: 'none', backgroundColor: '#e50914', color: '#fff', marginLeft: '10px' }}>Search</button>
      </form>
    </nav>
  );
}

export default Navbar;