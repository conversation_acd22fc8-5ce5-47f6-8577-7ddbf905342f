import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';

const API_KEY = '3308647fabe47a844ab269e6eab19132';

function Navbar() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showResults, setShowResults] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const searchRef = useRef(null);
  const debounceRef = useRef(null);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle click outside search
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Debounced search function
  const performSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await axios.get(
        `https://api.themoviedb.org/3/search/multi?api_key=${API_KEY}&query=${encodeURIComponent(query)}`
      );

      const results = response.data.results
        .filter(item => item.poster_path && (item.media_type === 'movie' || item.media_type === 'tv'))
        .slice(0, 8);

      setSearchResults(results);
      setShowResults(true);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearchChange = (event) => {
    const query = event.target.value;
    setSearchQuery(query);

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Debounce search
    debounceRef.current = setTimeout(() => {
      performSearch(query);
    }, 300);
  };

  const handleSearchSubmit = (event) => {
    event.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
      setShowResults(false);
      setSearchQuery('');
    }
  };

  const handleResultClick = (item) => {
    const path = item.media_type === 'tv' ? `/series/${item.id}` : `/movie/${item.id}`;
    navigate(path);
    setShowResults(false);
    setSearchQuery('');
  };

  const isActive = (path) => {
    return location.pathname === path ||
           (path === '/movies' && location.pathname.startsWith('/movie')) ||
           (path === '/series' && location.pathname.startsWith('/series'));
  };

  return (
    <nav className={`${isScrolled ? 'scrolled' : ''}`}>
      <div className="nav-container">
        <Link to="/" className="nav-logo">
          🎬 TMDB
        </Link>

        {/* Desktop Navigation */}
        <div className="nav-links">
          <Link
            to="/"
            className={`nav-link ${isActive('/') ? 'active' : ''}`}
          >
            Home
          </Link>
          <Link
            to="/movies"
            className={`nav-link ${isActive('/movies') ? 'active' : ''}`}
          >
            Movies
          </Link>
          <Link
            to="/series"
            className={`nav-link ${isActive('/series') ? 'active' : ''}`}
          >
            Series
          </Link>
        </div>

        {/* Search Container */}
        <div className="search-container" ref={searchRef}>
          <form className="search-form" onSubmit={handleSearchSubmit}>
            <input
              type="text"
              placeholder="Search movies, TV shows..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="search-input"
              autoComplete="off"
            />
            <button type="submit" className="search-button" disabled={isSearching}>
              {isSearching ? (
                <div className="spinner" style={{ width: '16px', height: '16px' }}></div>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              )}
            </button>
          </form>

          {/* Search Results Dropdown */}
          {showResults && (
            <div className="search-results">
              {searchResults.length > 0 ? (
                <>
                  {searchResults.map((item) => (
                    <div
                      key={`${item.media_type}-${item.id}`}
                      className="search-result-item"
                      onClick={() => handleResultClick(item)}
                    >
                      <img
                        src={`https://image.tmdb.org/t/p/w92${item.poster_path}`}
                        alt={item.media_type === 'tv' ? item.name : item.title}
                        className="search-result-poster"
                      />
                      <div className="search-result-info">
                        <div className="search-result-title">
                          {item.media_type === 'tv' ? item.name : item.title}
                        </div>
                        <div className="search-result-meta">
                          <span className="search-result-type">
                            {item.media_type === 'tv' ? 'TV Series' : 'Movie'}
                          </span>
                          <span className="search-result-year">
                            {item.media_type === 'tv'
                              ? new Date(item.first_air_date).getFullYear()
                              : new Date(item.release_date).getFullYear()
                            }
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="search-result-footer">
                    <button
                      className="search-view-all"
                      onClick={() => handleSearchSubmit({ preventDefault: () => {} })}
                    >
                      View all results for "{searchQuery}"
                    </button>
                  </div>
                </>
              ) : (
                <div className="search-no-results">
                  <p>No results found for "{searchQuery}"</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-button"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          aria-label="Toggle mobile menu"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
          </svg>
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="mobile-menu">
          <Link
            to="/"
            className={`mobile-nav-link ${isActive('/') ? 'active' : ''}`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Home
          </Link>
          <Link
            to="/movies"
            className={`mobile-nav-link ${isActive('/movies') ? 'active' : ''}`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Movies
          </Link>
          <Link
            to="/series"
            className={`mobile-nav-link ${isActive('/series') ? 'active' : ''}`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Series
          </Link>
        </div>
      )}
    </nav>
  );
}

export default Navbar;