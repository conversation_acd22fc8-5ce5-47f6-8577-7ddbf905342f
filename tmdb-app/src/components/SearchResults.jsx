import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import axios from 'axios';

const API_KEY = '3308647fabe47a844ab269e6eab19132';

// Movie card component for search results
const SearchResultCard = ({ item }) => (
  <div className="movie-card fade-in-up">
    <Link to={item.media_type === 'tv' ? `/series/${item.id}` : `/movie/${item.id}`}>
      <div className="movie-poster-container">
        <img
          className="movie-poster"
          src={item.poster_path 
            ? `https://image.tmdb.org/t/p/w500${item.poster_path}`
            : '/api/placeholder/300/450'
          }
          alt={item.media_type === 'tv' ? item.name : item.title}
          loading="lazy"
        />
        <div className="movie-overlay">
          <button className="play-button" aria-label="View details">
            <svg className="play-icon" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </button>
        </div>
      </div>
      <div className="movie-info">
        <h3 className="movie-title">
          {item.media_type === 'tv' ? item.name : item.title}
        </h3>
        <div className="movie-meta">
          <span className="movie-year">
            {item.media_type === 'tv' 
              ? new Date(item.first_air_date).getFullYear() || 'N/A'
              : new Date(item.release_date).getFullYear() || 'N/A'
            }
          </span>
          <div className="movie-rating">
            <svg className="star-icon" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span>{item.vote_average?.toFixed(1) || 'N/A'}</span>
          </div>
        </div>
        <div className="movie-genres">
          <span className="genre-tag">
            {item.media_type === 'tv' ? 'TV Series' : 'Movie'}
          </span>
        </div>
      </div>
    </Link>
  </div>
);

// Loading skeleton component
const SkeletonCard = () => (
  <div className="skeleton-card">
    <div className="skeleton skeleton-poster"></div>
    <div className="skeleton-info">
      <div className="skeleton skeleton-title"></div>
      <div className="skeleton skeleton-meta"></div>
    </div>
  </div>
);

function SearchResults() {
  const [searchParams] = useSearchParams();
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalResults, setTotalResults] = useState(0);

  const query = searchParams.get('q');

  useEffect(() => {
    if (query) {
      searchMovies(query, 1);
    }
  }, [query]);

  const searchMovies = async (searchQuery, page = 1) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(
        `https://api.themoviedb.org/3/search/multi?api_key=${API_KEY}&query=${encodeURIComponent(searchQuery)}&page=${page}`
      );

      const filteredResults = response.data.results.filter(
        item => item.media_type === 'movie' || item.media_type === 'tv'
      );

      setResults(filteredResults);
      setTotalPages(response.data.total_pages);
      setTotalResults(response.data.total_results);
      setCurrentPage(page);
    } catch (error) {
      console.error('Search error:', error);
      setError('Failed to search. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      searchMovies(query, page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  if (!query) {
    return (
      <div className="container">
        <div className="text-center mt-4">
          <h2>No search query provided</h2>
          <Link to="/" className="btn btn-primary">Go Home</Link>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container">
        <div className="error-message text-center">
          <h2>Oops! Something went wrong</h2>
          <p>{error}</p>
          <button 
            className="btn btn-primary mt-2" 
            onClick={() => searchMovies(query, currentPage)}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container fade-in">
      <div className="search-results-header">
        <h1>Search Results for "{query}"</h1>
        {!loading && (
          <p className="search-results-count">
            Found {totalResults} results
          </p>
        )}
      </div>

      {loading ? (
        <div className="movie-grid">
          {Array(20).fill().map((_, index) => (
            <SkeletonCard key={index} />
          ))}
        </div>
      ) : results.length > 0 ? (
        <>
          <div className="movie-grid">
            {results.map((item) => (
              <SearchResultCard 
                key={`${item.media_type}-${item.id}`} 
                item={item} 
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination">
              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </button>
              
              <div className="pagination-info">
                Page {currentPage} of {totalPages}
              </div>
              
              <button
                className="pagination-btn"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </button>
            </div>
          )}
        </>
      ) : (
        <div className="no-results">
          <div className="no-results-content">
            <h2>No results found</h2>
            <p>We couldn't find anything matching "{query}"</p>
            <p>Try searching with different keywords or check your spelling.</p>
            <Link to="/" className="btn btn-primary">
              Back to Home
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchResults;
