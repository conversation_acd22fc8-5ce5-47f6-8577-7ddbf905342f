import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';

const API_KEY = '3308647fabe47a844ab269e6eab19132'; // Replace with your actual API key

function Home() {
  const [popularMovies, setPopularMovies] = useState([]);
  const [topRatedMovies, setTopRatedMovies] = useState([]);
  const [upcomingMovies, setUpcomingMovies] = useState([]);
  const [popularSeries, setPopularSeries] = useState([]);

  useEffect(() => {
    async function fetchData() {
      try {
        const popularMoviesResponse = await axios.get(
          `https://api.themoviedb.org/3/movie/popular?api_key=${API_KEY}`
        );
        setPopularMovies(popularMoviesResponse.data.results);

        const topRatedMoviesResponse = await axios.get(
          `https://api.themoviedb.org/3/movie/top_rated?api_key=${API_KEY}`
        );
        setTopRatedMovies(topRatedMoviesResponse.data.results);

        const upcomingMoviesResponse = await axios.get(
          `https://api.themoviedb.org/3/movie/upcoming?api_key=${API_KEY}`
        );
        setUpcomingMovies(upcomingMoviesResponse.data.results);

        const popularSeriesResponse = await axios.get(
          `https://api.themoviedb.org/3/tv/popular?api_key=${API_KEY}`
        );
        setPopularSeries(popularSeriesResponse.data.results);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    }
    fetchData();
  }, []);

  return (
    <div>
      <h1>Popular Movies</h1>
      <div className="movie-list">
        {popularMovies.map((movie) => (
          <div className="movie-card" key={movie.id}>
            <Link to={`/movie/${movie.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${movie.poster_path}`}
                alt={movie.title}
              />
              <p>{movie.title}</p>
            </Link>
          </div>
        ))}
      </div>

      <h1>Top Rated Movies</h1>
      <div className="movie-list">
        {topRatedMovies.map((movie) => (
          <div className="movie-card" key={movie.id}>
            <Link to={`/movie/${movie.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${movie.poster_path}`}
                alt={movie.title}
              />
              <p>{movie.title}</p>
            </Link>
          </div>
        ))}
      </div>

      <h1>Upcoming Movies</h1>
      <div className="movie-list">
        {upcomingMovies.map((movie) => (
          <div className="movie-card" key={movie.id}>
            <Link to={`/movie/${movie.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${movie.poster_path}`}
                alt={movie.title}
              />
              <p>{movie.title}</p>
            </Link>
          </div>
        ))}
      </div>

      <h1>Popular Series</h1>
      <div className="movie-list">
        {popularSeries.map((series) => (
          <div className="movie-card" key={series.id}>
            <Link to={`/movie/${series.id}`}>
              <img
                src={`https://image.tmdb.org/t/p/w200/${series.poster_path}`}
                alt={series.name}
              />
              <p>{series.name}</p>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Home;