import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';

const API_KEY = '3308647fabe47a844ab269e6eab19132';

// Loading skeleton component
const SkeletonCard = () => (
  <div className="skeleton-card">
    <div className="skeleton skeleton-poster"></div>
    <div className="skeleton-info">
      <div className="skeleton skeleton-title"></div>
      <div className="skeleton skeleton-meta"></div>
    </div>
  </div>
);

// Movie card component
const MovieCard = ({ movie, isTV = false }) => (
  <div className="movie-card fade-in-up">
    <Link to={`/movie/${movie.id}`}>
      <div className="movie-poster-container">
        <img
          className="movie-poster"
          src={movie.poster_path
            ? `https://image.tmdb.org/t/p/w500${movie.poster_path}`
            : '/api/placeholder/300/450'
          }
          alt={isTV ? movie.name : movie.title}
          loading="lazy"
        />
        <div className="movie-overlay">
          <button className="play-button" aria-label="Play movie">
            <svg className="play-icon" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      </div>
      <div className="movie-info">
        <h3 className="movie-title">{isTV ? movie.name : movie.title}</h3>
        <div className="movie-meta">
          <span className="movie-year">
            {isTV
              ? new Date(movie.first_air_date).getFullYear() || 'N/A'
              : new Date(movie.release_date).getFullYear() || 'N/A'
            }
          </span>
          <div className="movie-rating">
            <svg className="star-icon" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span>{movie.vote_average?.toFixed(1) || 'N/A'}</span>
          </div>
        </div>
      </div>
    </Link>
  </div>
);

function Home() {
  const [popularMovies, setPopularMovies] = useState([]);
  const [topRatedMovies, setTopRatedMovies] = useState([]);
  const [upcomingMovies, setUpcomingMovies] = useState([]);
  const [popularSeries, setPopularSeries] = useState([]);
  const [featuredMovie, setFeaturedMovie] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        const [popularRes, topRatedRes, upcomingRes, seriesRes] = await Promise.all([
          axios.get(`https://api.themoviedb.org/3/movie/popular?api_key=${API_KEY}`),
          axios.get(`https://api.themoviedb.org/3/movie/top_rated?api_key=${API_KEY}`),
          axios.get(`https://api.themoviedb.org/3/movie/upcoming?api_key=${API_KEY}`),
          axios.get(`https://api.themoviedb.org/3/tv/popular?api_key=${API_KEY}`)
        ]);

        const popularMoviesData = popularRes.data.results;
        const topRatedMoviesData = topRatedRes.data.results;
        const upcomingMoviesData = upcomingRes.data.results;
        const popularSeriesData = seriesRes.data.results;

        setPopularMovies(popularMoviesData);
        setTopRatedMovies(topRatedMoviesData);
        setUpcomingMovies(upcomingMoviesData);
        setPopularSeries(popularSeriesData);

        // Set featured movie (first popular movie with backdrop)
        const featured = popularMoviesData.find(movie => movie.backdrop_path) || popularMoviesData[0];
        if (featured) {
          // Fetch detailed info for featured movie
          const detailsRes = await axios.get(`https://api.themoviedb.org/3/movie/${featured.id}?api_key=${API_KEY}`);
          setFeaturedMovie(detailsRes.data);
        }

      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Failed to load movies. Please try again later.');
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  if (error) {
    return (
      <div className="container">
        <div className="error-message text-center">
          <h2>Oops! Something went wrong</h2>
          <p>{error}</p>
          <button
            className="btn btn-primary mt-2"
            onClick={() => window.location.reload()}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fade-in">
      {/* Hero Section */}
      {featuredMovie && (
        <section className="hero-section">
          <img
            className="hero-background"
            src={`https://image.tmdb.org/t/p/original${featuredMovie.backdrop_path}`}
            alt={featuredMovie.title}
          />
          <div className="hero-overlay"></div>
          <div className="container">
            <div className="hero-content">
              <h1 className="hero-title">{featuredMovie.title}</h1>
              <p className="hero-description">
                {featuredMovie.overview?.length > 200
                  ? `${featuredMovie.overview.substring(0, 200)}...`
                  : featuredMovie.overview
                }
              </p>
              <div className="hero-buttons">
                <Link to={`/movie/${featuredMovie.id}`} className="btn btn-primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  Watch Now
                </Link>
                <Link to={`/movie/${featuredMovie.id}`} className="btn btn-secondary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                  More Info
                </Link>
              </div>
            </div>
          </div>
        </section>
      )}

      <div className="container">
        {/* Popular Movies Section */}
        <section className="section">
          <div className="section-header">
            <h2 className="section-title">🔥 Trending Now</h2>
            <Link to="/movies" className="btn btn-secondary">View All</Link>
          </div>
          <div className="movie-list">
            {loading ? (
              Array(10).fill().map((_, index) => <SkeletonCard key={index} />)
            ) : (
              popularMovies.slice(0, 10).map((movie) => (
                <MovieCard key={movie.id} movie={movie} />
              ))
            )}
          </div>
        </section>

        {/* Top Rated Movies Section */}
        <section className="section">
          <div className="section-header">
            <h2 className="section-title">⭐ Top Rated</h2>
            <Link to="/movies" className="btn btn-secondary">View All</Link>
          </div>
          <div className="movie-list">
            {loading ? (
              Array(10).fill().map((_, index) => <SkeletonCard key={index} />)
            ) : (
              topRatedMovies.slice(0, 10).map((movie) => (
                <MovieCard key={movie.id} movie={movie} />
              ))
            )}
          </div>
        </section>

        {/* Upcoming Movies Section */}
        <section className="section">
          <div className="section-header">
            <h2 className="section-title">🎬 Coming Soon</h2>
            <Link to="/movies" className="btn btn-secondary">View All</Link>
          </div>
          <div className="movie-list">
            {loading ? (
              Array(10).fill().map((_, index) => <SkeletonCard key={index} />)
            ) : (
              upcomingMovies.slice(0, 10).map((movie) => (
                <MovieCard key={movie.id} movie={movie} />
              ))
            )}
          </div>
        </section>

        {/* Popular Series Section */}
        <section className="section">
          <div className="section-header">
            <h2 className="section-title">📺 Popular Series</h2>
            <Link to="/series" className="btn btn-secondary">View All</Link>
          </div>
          <div className="movie-list">
            {loading ? (
              Array(10).fill().map((_, index) => <SkeletonCard key={index} />)
            ) : (
              popularSeries.slice(0, 10).map((series) => (
                <MovieCard key={series.id} movie={series} isTV={true} />
              ))
            )}
          </div>
        </section>
      </div>
    </div>
  );
}

export default Home;