import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, Link } from 'react-router-dom';
import axios from 'axios';

const API_KEY = '3308647fabe47a844ab269e6eab19132';

// Server configurations
const SERVERS = [
  {
    id: 'autoembed',
    name: 'AutoEmbed Player',
    description: 'High quality streaming with multiple sources',
    getUrl: (type, id, season, episode) => {
      if (type === 'tv') {
        return `https://player.autoembed.cc/embed/tv/${id}/${season}/${episode}`;
      }
      return `https://player.autoembed.cc/embed/movie/${id}`;
    }
  },
  {
    id: 'autoembed2',
    name: 'AutoEmbed Alternative',
    description: 'Alternative streaming source',
    getUrl: (type, id, season, episode) => {
      if (type === 'tv') {
        return `https://autoembed.co/tv/tmdb/${id}-${season}-${episode}`;
      }
      return `https://autoembed.co/movie/tmdb/${id}`;
    }
  },
  {
    id: 'superembed',
    name: 'SuperEmbed',
    description: 'Premium streaming experience',
    getUrl: (type, id, season, episode, imdbId) => {
      const useId = imdbId || id;
      if (type === 'tv') {
        return `https://www.superembed.stream/embed/${useId}/${season}/${episode}`;
      }
      return `https://www.superembed.stream/embed/${useId}`;
    }
  },
  {
    id: 'vidsrc',
    name: 'VidSrc Player',
    description: 'Fast and reliable streaming',
    getUrl: (type, id, season, episode, imdbId) => {
      const useId = imdbId || id;
      if (type === 'tv') {
        return `https://vidsrc.me/embed/tv?tmdb=${id}&season=${season}&episode=${episode}`;
      }
      return `https://vidsrc.me/embed/movie?tmdb=${id}`;
    }
  }
];

function PlayPage() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  
  const type = searchParams.get('type') || 'movie';
  const season = searchParams.get('season') || '1';
  const episode = searchParams.get('episode') || '1';
  const title = searchParams.get('title') || '';
  const imdbId = searchParams.get('imdb') || '';
  
  const [selectedServer, setSelectedServer] = useState(SERVERS[0].id);
  const [isLoading, setIsLoading] = useState(true);
  const [contentInfo, setContentInfo] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchContentInfo();
  }, [id, type]);

  const fetchContentInfo = async () => {
    try {
      setIsLoading(true);
      const mediaType = type === 'tv' ? 'tv' : 'movie';
      const response = await axios.get(
        `https://api.themoviedb.org/3/${mediaType}/${id}?api_key=${API_KEY}`
      );
      setContentInfo(response.data);
    } catch (error) {
      console.error('Error fetching content info:', error);
      setError('Failed to load content information');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentStreamingUrl = () => {
    const server = SERVERS.find(s => s.id === selectedServer);
    if (!server) return '';
    
    return server.getUrl(type, id, season, episode, imdbId);
  };

  const getDisplayTitle = () => {
    if (contentInfo) {
      const baseTitle = type === 'tv' ? contentInfo.name : contentInfo.title;
      if (type === 'tv') {
        return `${baseTitle} - S${season}E${episode}`;
      }
      return baseTitle;
    }
    return title || 'Loading...';
  };

  if (error) {
    return (
      <div className="container">
        <div className="play-error">
          <h2>Unable to Load Content</h2>
          <p>{error}</p>
          <Link to="/" className="btn btn-primary">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="play-page">
      {/* Header */}
      <div className="play-header">
        <div className="container">
          <div className="play-nav">
            <Link to="/" className="play-back-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
              </svg>
              Back
            </Link>
            <h1 className="play-title">{getDisplayTitle()}</h1>
          </div>
        </div>
      </div>

      {/* Player Section */}
      <div className="play-content">
        <div className="container">
          <div className="play-layout">
            {/* Video Player */}
            <div className="play-player-section">
              <div className="play-player-container">
                {isLoading ? (
                  <div className="play-loading">
                    <div className="spinner"></div>
                    <p>Loading player...</p>
                  </div>
                ) : (
                  <iframe
                    src={getCurrentStreamingUrl()}
                    title={getDisplayTitle()}
                    className="play-iframe"
                    allowFullScreen
                    style={{ border: 'none' }}
                    referrerPolicy="origin"
                    sandbox="allow-same-origin allow-scripts allow-forms"
                  />
                )}
              </div>
            </div>

            {/* Server Selection Sidebar */}
            <div className="play-sidebar">
              <div className="server-selection">
                <h3 className="server-title">Choose Server</h3>
                <div className="server-list">
                  {SERVERS.map((server) => (
                    <button
                      key={server.id}
                      className={`server-btn ${selectedServer === server.id ? 'active' : ''}`}
                      onClick={() => {
                        setSelectedServer(server.id);
                        setIsLoading(true);
                        setTimeout(() => setIsLoading(false), 1000);
                      }}
                    >
                      <div className="server-info">
                        <div className="server-name">{server.name}</div>
                        <div className="server-desc">{server.description}</div>
                      </div>
                      {selectedServer === server.id && (
                        <div className="server-indicator">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Content Info */}
              {contentInfo && (
                <div className="content-info">
                  <h3>About</h3>
                  <div className="content-details">
                    <img
                      src={contentInfo.poster_path 
                        ? `https://image.tmdb.org/t/p/w300${contentInfo.poster_path}`
                        : '/api/placeholder/150/225'
                      }
                      alt={type === 'tv' ? contentInfo.name : contentInfo.title}
                      className="content-poster"
                    />
                    <div className="content-meta">
                      <p className="content-overview">
                        {contentInfo.overview?.substring(0, 200)}
                        {contentInfo.overview?.length > 200 ? '...' : ''}
                      </p>
                      <div className="content-stats">
                        <span className="stat">
                          ⭐ {contentInfo.vote_average?.toFixed(1)}/10
                        </span>
                        <span className="stat">
                          📅 {type === 'tv' 
                            ? new Date(contentInfo.first_air_date).getFullYear()
                            : new Date(contentInfo.release_date).getFullYear()
                          }
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PlayPage;
