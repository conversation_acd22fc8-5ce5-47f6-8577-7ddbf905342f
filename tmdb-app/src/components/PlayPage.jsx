import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, Link } from 'react-router-dom';
import axios from 'axios';

const API_KEY = import.meta.env.VITE_TMDB_API_KEY;

// Updated Server configurations with working APIs
const SERVERS = [
  {
    id: 'vidsrcpro',
    name: 'VidSrc Pro',
    description: 'High quality streaming - Most reliable',
    getUrl: (type, id, season, episode, imdbId) => {
      if (type === 'tv') {
        return `https://vidsrc.pro/embed/tv/${id}/${season}/${episode}`;
      }
      return `https://vidsrc.pro/embed/movie/${id}`;
    }
  },
  {
    id: 'vidsrcxyz',
    name: 'VidSrc XYZ',
    description: 'Alternative VidSrc server',
    getUrl: (type, id, season, episode, imdbId) => {
      if (type === 'tv') {
        return `https://vidsrc.xyz/embed/tv?tmdb=${id}&season=${season}&episode=${episode}`;
      }
      return `https://vidsrc.xyz/embed/movie?tmdb=${id}`;
    }
  },
  {
    id: 'vidsrcto',
    name: 'VidSrc TO',
    description: 'Fast streaming with multiple sources',
    getUrl: (type, id, season, episode, imdbId) => {
      if (type === 'tv') {
        return `https://vidsrc.to/embed/tv/${id}/${season}/${episode}`;
      }
      return `https://vidsrc.to/embed/movie/${id}`;
    }
  },
  {
    id: 'multiembed',
    name: 'MultiEmbed',
    description: 'Multiple streaming sources',
    getUrl: (type, id, season, episode, imdbId) => {
      if (type === 'tv') {
        return `https://multiembed.mov/directstream.php?video_id=${id}&tmdb=1&s=${season}&e=${episode}`;
      }
      return `https://multiembed.mov/directstream.php?video_id=${id}&tmdb=1`;
    }
  },
  {
    id: 'embedsu',
    name: 'Embed SU',
    description: 'Reliable backup server',
    getUrl: (type, id, season, episode, imdbId) => {
      if (type === 'tv') {
        return `https://embed.su/embed/tv/${id}/${season}/${episode}`;
      }
      return `https://embed.su/embed/movie/${id}`;
    }
  },
  {
    id: 'smashystream',
    name: 'Smashy Stream',
    description: 'Fast and stable streaming',
    getUrl: (type, id, season, episode, imdbId) => {
      if (type === 'tv') {
        return `https://player.smashy.stream/tv/${id}?s=${season}&e=${episode}`;
      }
      return `https://player.smashy.stream/movie/${id}`;
    }
  }
];

function PlayPage() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  
  const type = searchParams.get('type') || 'movie';
  const season = searchParams.get('season') || '1';
  const episode = searchParams.get('episode') || '1';
  const title = searchParams.get('title') || '';
  const imdbId = searchParams.get('imdb') || '';
  
  const [selectedServer, setSelectedServer] = useState(SERVERS[0].id);
  const [isLoading, setIsLoading] = useState(true);
  const [contentInfo, setContentInfo] = useState(null);
  const [error, setError] = useState(null);
  const [iframeError, setIframeError] = useState(false);
  const [serverStatus, setServerStatus] = useState({});
  const [autoRetryCount, setAutoRetryCount] = useState(0);

  useEffect(() => {
    fetchContentInfo();
  }, [id, type]);

  const fetchContentInfo = async () => {
    try {
      setIsLoading(true);
      const mediaType = type === 'tv' ? 'tv' : 'movie';

      // Fetch content details with external IDs
      const [detailsRes, externalRes] = await Promise.all([
        axios.get(`https://api.themoviedb.org/3/${mediaType}/${id}?api_key=${API_KEY}`),
        axios.get(`https://api.themoviedb.org/3/${mediaType}/${id}/external_ids?api_key=${API_KEY}`)
      ]);

      const contentData = detailsRes.data;
      const externalData = externalRes.data;

      // Add IMDB ID to content info
      contentData.imdb_id = externalData.imdb_id;

      setContentInfo(contentData);

      console.log('Content Info:', contentData);
      console.log('IMDB ID:', externalData.imdb_id);
      console.log('Current URL:', getCurrentStreamingUrl());

    } catch (error) {
      console.error('Error fetching content info:', error);
      setError('Failed to load content information');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentStreamingUrl = () => {
    const server = SERVERS.find(s => s.id === selectedServer);
    if (!server) return '';

    // Use IMDB ID from content info if available, otherwise use URL param or TMDB ID
    const useImdbId = contentInfo?.imdb_id || imdbId;
    const url = server.getUrl(type, id, season, episode, useImdbId);

    console.log('🎬 Streaming Info:', {
      server: server.name,
      url: url,
      type: type,
      tmdbId: id,
      imdbId: useImdbId,
      season: season,
      episode: episode
    });

    return url;
  };

  // Auto-retry with next server if current one fails
  const handleServerFailure = () => {
    console.log('❌ Server failed, trying next server...');

    const currentIndex = SERVERS.findIndex(s => s.id === selectedServer);
    const nextIndex = (currentIndex + 1) % SERVERS.length;

    if (autoRetryCount < SERVERS.length - 1) {
      setAutoRetryCount(prev => prev + 1);
      setSelectedServer(SERVERS[nextIndex].id);
      setIframeError(false);
      setIsLoading(true);

      console.log(`🔄 Switching to ${SERVERS[nextIndex].name} (attempt ${autoRetryCount + 1})`);
    } else {
      console.log('❌ All servers failed');
      setIframeError(true);
    }
  };

  const getDisplayTitle = () => {
    if (contentInfo) {
      const baseTitle = type === 'tv' ? contentInfo.name : contentInfo.title;
      if (type === 'tv') {
        return `${baseTitle} - S${season}E${episode}`;
      }
      return baseTitle;
    }
    return title || 'Loading...';
  };

  if (error) {
    return (
      <div className="container">
        <div className="play-error">
          <h2>Unable to Load Content</h2>
          <p>{error}</p>
          <Link to="/" className="btn btn-primary">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="play-page">
      {/* Header */}
      <div className="play-header">
        <div className="container">
          <div className="play-nav">
            <Link to="/" className="play-back-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
              </svg>
              Back
            </Link>
            <h1 className="play-title">{getDisplayTitle()}</h1>
          </div>
        </div>
      </div>

      {/* Player Section */}
      <div className="play-content">
        <div className="container">
          <div className="play-layout">
            {/* Video Player */}
            <div className="play-player-section">
              <div className="play-player-container">
                {isLoading ? (
                  <div className="play-loading">
                    <div className="spinner"></div>
                    <p>Loading player...</p>
                  </div>
                ) : (
                  <>
                    <iframe
                      key={`${selectedServer}-${id}-${type}-${season}-${episode}`}
                      src={getCurrentStreamingUrl()}
                      title={getDisplayTitle()}
                      className="play-iframe"
                      allowFullScreen
                      style={{ border: 'none' }}
                      referrerPolicy="no-referrer-when-downgrade"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
                      onLoad={() => {
                        console.log('✅ Iframe loaded successfully');
                        setIframeError(false);
                        setIsLoading(false);
                        setAutoRetryCount(0); // Reset retry count on success
                      }}
                      onError={() => {
                        console.log('❌ Iframe failed to load');
                        setTimeout(() => {
                          handleServerFailure();
                        }, 2000); // Wait 2 seconds before trying next server
                      }}
                    />
                    {iframeError && (
                      <div className="iframe-error-overlay">
                        <div className="error-content">
                          <h3>🚫 Streaming Unavailable</h3>
                          <p>This content is currently unavailable on all servers.</p>
                          <div className="error-details">
                            <p><strong>Tried {autoRetryCount + 1} of {SERVERS.length} servers</strong></p>
                            <p>Current server: {SERVERS.find(s => s.id === selectedServer)?.name}</p>
                          </div>
                          <div className="error-actions">
                            <button
                              onClick={() => {
                                setAutoRetryCount(0);
                                setSelectedServer(SERVERS[0].id);
                                setIframeError(false);
                                setIsLoading(true);
                              }}
                              className="retry-btn"
                            >
                              🔄 Try Again
                            </button>
                            <button
                              onClick={() => window.open(getCurrentStreamingUrl(), '_blank')}
                              className="external-btn"
                            >
                              🔗 Open Externally
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Server Selection Sidebar */}
            <div className="play-sidebar">
              <div className="server-selection">
                <h3 className="server-title">Choose Server</h3>
                <div className="server-list">
                  {SERVERS.map((server) => (
                    <button
                      key={server.id}
                      className={`server-btn ${selectedServer === server.id ? 'active' : ''}`}
                      onClick={() => {
                        console.log('🔄 Manually switching to server:', server.name);
                        setSelectedServer(server.id);
                        setIsLoading(true);
                        setIframeError(false);
                        setAutoRetryCount(0); // Reset retry count on manual selection

                        // Force iframe reload by changing key
                        setTimeout(() => {
                          setIsLoading(false);
                          console.log('🎬 New URL:', server.getUrl(type, id, season, episode, contentInfo?.imdb_id || imdbId));
                        }, 1000);
                      }}
                    >
                      <div className="server-info">
                        <div className="server-name">
                          {server.name}
                          {autoRetryCount > 0 && SERVERS.findIndex(s => s.id === server.id) < autoRetryCount && (
                            <span className="server-status failed">❌</span>
                          )}
                          {selectedServer === server.id && !iframeError && (
                            <span className="server-status active">✅</span>
                          )}
                        </div>
                        <div className="server-desc">{server.description}</div>
                      </div>
                      {selectedServer === server.id && (
                        <div className="server-indicator">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Enhanced Debug Panel */}
              <div className="debug-info">
                <h4>🔧 Streaming Debug Panel</h4>
                <div className="debug-grid">
                  <div className="debug-item">
                    <strong>Current Server:</strong> {SERVERS.find(s => s.id === selectedServer)?.name}
                  </div>
                  <div className="debug-item">
                    <strong>Streaming URL:</strong>
                    <div className="url-display">{getCurrentStreamingUrl()}</div>
                  </div>
                  <div className="debug-item">
                    <strong>Content ID:</strong> {id} (TMDB)
                  </div>
                  <div className="debug-item">
                    <strong>IMDB ID:</strong> {contentInfo?.imdb_id || imdbId || 'Not available'}
                  </div>
                  <div className="debug-item">
                    <strong>Type:</strong> {type === 'tv' ? `TV Series (S${season}E${episode})` : 'Movie'}
                  </div>
                  <div className="debug-item">
                    <strong>Status:</strong>
                    <span className={`status ${isLoading ? 'loading' : iframeError ? 'error' : 'success'}`}>
                      {isLoading ? '⏳ Loading...' : iframeError ? '❌ Failed' : '✅ Loaded'}
                    </span>
                  </div>
                </div>

                <div className="debug-actions">
                  <button
                    onClick={() => window.open(getCurrentStreamingUrl(), '_blank')}
                    className="debug-btn primary"
                  >
                    🔗 Test URL Directly
                  </button>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(getCurrentStreamingUrl());
                      alert('URL copied to clipboard!');
                    }}
                    className="debug-btn secondary"
                  >
                    📋 Copy URL
                  </button>
                  <button
                    onClick={() => {
                      setIsLoading(true);
                      setIframeError(false);
                      // Force iframe reload
                      setTimeout(() => setIsLoading(false), 2000);
                    }}
                    className="debug-btn secondary"
                  >
                    🔄 Reload Player
                  </button>
                </div>
              </div>

              {/* Content Info */}
              {contentInfo && (
                <div className="content-info">
                  <h3>About</h3>
                  <div className="content-details">
                    <img
                      src={contentInfo.poster_path
                        ? `https://image.tmdb.org/t/p/w300${contentInfo.poster_path}`
                        : '/api/placeholder/150/225'
                      }
                      alt={type === 'tv' ? contentInfo.name : contentInfo.title}
                      className="content-poster"
                    />
                    <div className="content-meta">
                      <p className="content-overview">
                        {contentInfo.overview?.substring(0, 200)}
                        {contentInfo.overview?.length > 200 ? '...' : ''}
                      </p>
                      <div className="content-stats">
                        <span className="stat">
                          ⭐ {contentInfo.vote_average?.toFixed(1)}/10
                        </span>
                        <span className="stat">
                          📅 {type === 'tv'
                            ? new Date(contentInfo.first_air_date).getFullYear()
                            : new Date(contentInfo.release_date).getFullYear()
                          }
                        </span>
                        {contentInfo.imdb_id && (
                          <span className="stat">
                            🎬 IMDB: {contentInfo.imdb_id}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PlayPage;
