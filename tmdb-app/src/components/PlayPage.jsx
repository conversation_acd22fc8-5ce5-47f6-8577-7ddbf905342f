import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, Link } from 'react-router-dom';
import axios from 'axios';

const API_KEY = import.meta.env.VITE_TMDB_API_KEY;

// Server configurations
const SERVERS = [
  {
    id: 'autoembed',
    name: 'AutoEmbed Player',
    description: 'High quality streaming with multiple sources',
    getUrl: (type, id, season, episode, imdbId) => {
      // Use IMDB ID if available, otherwise use TMDB ID
      const useId = imdbId || id;
      if (type === 'tv') {
        return `https://player.autoembed.cc/embed/tv/${useId}/${season}/${episode}`;
      }
      return `https://player.autoembed.cc/embed/movie/${useId}`;
    }
  },
  {
    id: 'autoembed2',
    name: 'AutoEmbed Alternative',
    description: 'Alternative streaming source',
    getUrl: (type, id, season, episode) => {
      // This API specifically uses TMDB IDs
      if (type === 'tv') {
        return `https://autoembed.co/tv/tmdb/${id}-${season}-${episode}`;
      }
      return `https://autoembed.co/movie/tmdb/${id}`;
    }
  },
  {
    id: 'vidsrc',
    name: 'VidSrc Player',
    description: 'Fast and reliable streaming',
    getUrl: (type, id, season, episode) => {
      // VidSrc uses TMDB IDs
      if (type === 'tv') {
        return `https://vidsrc.me/embed/tv?tmdb=${id}&season=${season}&episode=${episode}`;
      }
      return `https://vidsrc.me/embed/movie?tmdb=${id}`;
    }
  },
  {
    id: 'embedsu',
    name: 'Embed.su',
    description: 'Reliable streaming service',
    getUrl: (type, id, season, episode, imdbId) => {
      // Use IMDB ID if available, otherwise TMDB
      const useId = imdbId || id;
      if (type === 'tv') {
        return `https://embed.su/embed/tv/${useId}/${season}/${episode}`;
      }
      return `https://embed.su/embed/movie/${useId}`;
    }
  }
];

function PlayPage() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  
  const type = searchParams.get('type') || 'movie';
  const season = searchParams.get('season') || '1';
  const episode = searchParams.get('episode') || '1';
  const title = searchParams.get('title') || '';
  const imdbId = searchParams.get('imdb') || '';
  
  const [selectedServer, setSelectedServer] = useState(SERVERS[0].id);
  const [isLoading, setIsLoading] = useState(true);
  const [contentInfo, setContentInfo] = useState(null);
  const [error, setError] = useState(null);
  const [iframeError, setIframeError] = useState(false);

  useEffect(() => {
    fetchContentInfo();
  }, [id, type]);

  const fetchContentInfo = async () => {
    try {
      setIsLoading(true);
      const mediaType = type === 'tv' ? 'tv' : 'movie';

      // Fetch content details with external IDs
      const [detailsRes, externalRes] = await Promise.all([
        axios.get(`https://api.themoviedb.org/3/${mediaType}/${id}?api_key=${API_KEY}`),
        axios.get(`https://api.themoviedb.org/3/${mediaType}/${id}/external_ids?api_key=${API_KEY}`)
      ]);

      const contentData = detailsRes.data;
      const externalData = externalRes.data;

      // Add IMDB ID to content info
      contentData.imdb_id = externalData.imdb_id;

      setContentInfo(contentData);

      console.log('Content Info:', contentData);
      console.log('IMDB ID:', externalData.imdb_id);
      console.log('Current URL:', getCurrentStreamingUrl());

    } catch (error) {
      console.error('Error fetching content info:', error);
      setError('Failed to load content information');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentStreamingUrl = () => {
    const server = SERVERS.find(s => s.id === selectedServer);
    if (!server) return '';

    // Use IMDB ID from content info if available, otherwise use URL param or TMDB ID
    const useImdbId = contentInfo?.imdb_id || imdbId;
    const url = server.getUrl(type, id, season, episode, useImdbId);

    console.log('Streaming URL:', url);
    console.log('Server:', server.name);
    console.log('Type:', type);
    console.log('ID:', id);
    console.log('IMDB ID:', useImdbId);
    console.log('Season/Episode:', season, episode);

    return url;
  };

  const getDisplayTitle = () => {
    if (contentInfo) {
      const baseTitle = type === 'tv' ? contentInfo.name : contentInfo.title;
      if (type === 'tv') {
        return `${baseTitle} - S${season}E${episode}`;
      }
      return baseTitle;
    }
    return title || 'Loading...';
  };

  if (error) {
    return (
      <div className="container">
        <div className="play-error">
          <h2>Unable to Load Content</h2>
          <p>{error}</p>
          <Link to="/" className="btn btn-primary">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="play-page">
      {/* Header */}
      <div className="play-header">
        <div className="container">
          <div className="play-nav">
            <Link to="/" className="play-back-btn">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.42-1.41L7.83 13H20v-2z"/>
              </svg>
              Back
            </Link>
            <h1 className="play-title">{getDisplayTitle()}</h1>
          </div>
        </div>
      </div>

      {/* Player Section */}
      <div className="play-content">
        <div className="container">
          <div className="play-layout">
            {/* Video Player */}
            <div className="play-player-section">
              <div className="play-player-container">
                {isLoading ? (
                  <div className="play-loading">
                    <div className="spinner"></div>
                    <p>Loading player...</p>
                  </div>
                ) : (
                  <>
                    <iframe
                      key={`${selectedServer}-${id}-${type}-${season}-${episode}`}
                      src={getCurrentStreamingUrl()}
                      title={getDisplayTitle()}
                      className="play-iframe"
                      allowFullScreen
                      style={{ border: 'none' }}
                      referrerPolicy="origin"
                      sandbox="allow-same-origin allow-scripts allow-forms allow-presentation allow-top-navigation-by-user-activation"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      onLoad={() => {
                        console.log('Iframe loaded successfully');
                        setIframeError(false);
                      }}
                      onError={() => {
                        console.log('Iframe failed to load');
                        setIframeError(true);
                      }}
                    />
                    {iframeError && (
                      <div style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        background: 'rgba(0,0,0,0.8)',
                        color: 'white',
                        padding: '1rem',
                        borderRadius: '8px',
                        textAlign: 'center'
                      }}>
                        <p>Failed to load this server. Try a different server or check the debug info.</p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Server Selection Sidebar */}
            <div className="play-sidebar">
              <div className="server-selection">
                <h3 className="server-title">Choose Server</h3>
                <div className="server-list">
                  {SERVERS.map((server) => (
                    <button
                      key={server.id}
                      className={`server-btn ${selectedServer === server.id ? 'active' : ''}`}
                      onClick={() => {
                        console.log('Switching to server:', server.name);
                        setSelectedServer(server.id);
                        setIsLoading(true);
                        // Force iframe reload by changing key
                        setTimeout(() => {
                          setIsLoading(false);
                          console.log('New URL:', server.getUrl(type, id, season, episode, contentInfo?.imdb_id || imdbId));
                        }, 1000);
                      }}
                    >
                      <div className="server-info">
                        <div className="server-name">{server.name}</div>
                        <div className="server-desc">{server.description}</div>
                      </div>
                      {selectedServer === server.id && (
                        <div className="server-indicator">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                          </svg>
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Debug Info (remove in production) */}
              <div className="debug-info" style={{
                background: '#333',
                padding: '1rem',
                borderRadius: '8px',
                fontSize: '0.8rem',
                marginBottom: '1rem'
              }}>
                <h4>Debug Info:</h4>
                <p><strong>Current URL:</strong> {getCurrentStreamingUrl()}</p>
                <p><strong>TMDB ID:</strong> {id}</p>
                <p><strong>IMDB ID:</strong> {contentInfo?.imdb_id || imdbId || 'Not available'}</p>
                <p><strong>Type:</strong> {type}</p>
                <p><strong>Season/Episode:</strong> {season}/{episode}</p>
                <p><strong>Server:</strong> {SERVERS.find(s => s.id === selectedServer)?.name}</p>
                <button
                  onClick={() => window.open(getCurrentStreamingUrl(), '_blank')}
                  style={{
                    background: '#e50914',
                    color: 'white',
                    border: 'none',
                    padding: '0.5rem 1rem',
                    borderRadius: '4px',
                    marginTop: '0.5rem',
                    cursor: 'pointer'
                  }}
                >
                  Test URL in New Tab
                </button>
              </div>

              {/* Content Info */}
              {contentInfo && (
                <div className="content-info">
                  <h3>About</h3>
                  <div className="content-details">
                    <img
                      src={contentInfo.poster_path
                        ? `https://image.tmdb.org/t/p/w300${contentInfo.poster_path}`
                        : '/api/placeholder/150/225'
                      }
                      alt={type === 'tv' ? contentInfo.name : contentInfo.title}
                      className="content-poster"
                    />
                    <div className="content-meta">
                      <p className="content-overview">
                        {contentInfo.overview?.substring(0, 200)}
                        {contentInfo.overview?.length > 200 ? '...' : ''}
                      </p>
                      <div className="content-stats">
                        <span className="stat">
                          ⭐ {contentInfo.vote_average?.toFixed(1)}/10
                        </span>
                        <span className="stat">
                          📅 {type === 'tv'
                            ? new Date(contentInfo.first_air_date).getFullYear()
                            : new Date(contentInfo.release_date).getFullYear()
                          }
                        </span>
                        {contentInfo.imdb_id && (
                          <span className="stat">
                            🎬 IMDB: {contentInfo.imdb_id}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PlayPage;
