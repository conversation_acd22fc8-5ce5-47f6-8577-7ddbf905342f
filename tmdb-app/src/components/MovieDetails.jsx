import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import axios from 'axios';

const API_KEY = '3308647fabe47a844ab269e6eab19132';

// Cast member card component
const CastCard = ({ person }) => (
  <div className="cast-card">
    <img
      className="cast-photo"
      src={person.profile_path
        ? `https://image.tmdb.org/t/p/w185${person.profile_path}`
        : '/api/placeholder/150/225'
      }
      alt={person.name}
      loading="lazy"
    />
    <div className="cast-info">
      <div className="cast-name">{person.name}</div>
      <div className="cast-character">{person.character}</div>
    </div>
  </div>
);

// Similar movie card component
const SimilarMovieCard = ({ movie }) => (
  <div className="movie-card">
    <Link to={`/movie/${movie.id}`}>
      <div className="movie-poster-container">
        <img
          className="movie-poster"
          src={movie.poster_path
            ? `https://image.tmdb.org/t/p/w300${movie.poster_path}`
            : '/api/placeholder/300/450'
          }
          alt={movie.title}
          loading="lazy"
        />
        <div className="movie-overlay">
          <button className="play-button" aria-label="View movie">
            <svg className="play-icon" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      </div>
      <div className="movie-info">
        <h3 className="movie-title">{movie.title}</h3>
        <div className="movie-meta">
          <span className="movie-year">
            {new Date(movie.release_date).getFullYear() || 'N/A'}
          </span>
          <div className="movie-rating">
            <svg className="star-icon" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span>{movie.vote_average?.toFixed(1) || 'N/A'}</span>
          </div>
        </div>
      </div>
    </Link>
  </div>
);

function MovieDetails() {
  const { id } = useParams();
  const [movie, setMovie] = useState(null);
  const [cast, setCast] = useState([]);
  const [crew, setCrew] = useState([]);
  const [similarMovies, setSimilarMovies] = useState([]);
  const [videos, setVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchMovieData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch movie details, credits, similar movies, and videos in parallel
        const [movieRes, creditsRes, similarRes, videosRes] = await Promise.all([
          axios.get(`https://api.themoviedb.org/3/movie/${id}?api_key=${API_KEY}`),
          axios.get(`https://api.themoviedb.org/3/movie/${id}/credits?api_key=${API_KEY}`),
          axios.get(`https://api.themoviedb.org/3/movie/${id}/similar?api_key=${API_KEY}`),
          axios.get(`https://api.themoviedb.org/3/movie/${id}/videos?api_key=${API_KEY}`)
        ]);

        setMovie(movieRes.data);
        setCast(creditsRes.data.cast.slice(0, 12)); // Show top 12 cast members
        setCrew(creditsRes.data.crew.filter(person =>
          ['Director', 'Producer', 'Writer', 'Screenplay'].includes(person.job)
        ));
        setSimilarMovies(similarRes.data.results.slice(0, 8));

        // Filter for trailers and teasers
        const movieVideos = videosRes.data.results.filter(video =>
          video.type === 'Trailer' || video.type === 'Teaser'
        );
        setVideos(movieVideos.slice(0, 3));

      } catch (error) {
        console.error('Error fetching movie data:', error);
        setError('Failed to load movie details. Please try again.');
      } finally {
        setLoading(false);
      }
    }

    if (id) {
      fetchMovieData();
    }
  }, [id]);

  const formatRuntime = (minutes) => {
    if (!minutes) return 'N/A';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatCurrency = (amount) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading-container">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  if (error || !movie) {
    return (
      <div className="container">
        <div className="error-message text-center">
          <h2>Movie not found</h2>
          <p>{error || 'The movie you are looking for does not exist.'}</p>
          <Link to="/" className="btn btn-primary">
            Back to Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="movie-details fade-in">
      {/* Hero Section with Backdrop */}
      {movie.backdrop_path && (
        <div className="hero-section" style={{ height: '60vh' }}>
          <img
            className="hero-background"
            src={`https://image.tmdb.org/t/p/original${movie.backdrop_path}`}
            alt={movie.title}
          />
          <div className="hero-overlay"></div>
        </div>
      )}

      <div className="container">
        {/* Movie Header */}
        <div className="movie-header">
          <img
            className="movie-poster-large"
            src={movie.poster_path
              ? `https://image.tmdb.org/t/p/w500${movie.poster_path}`
              : '/api/placeholder/300/450'
            }
            alt={movie.title}
          />

          <div className="movie-details-info">
            <h1>{movie.title}</h1>
            {movie.tagline && (
              <p className="movie-tagline">"{movie.tagline}"</p>
            )}

            <div className="movie-meta mb-3">
              <span className="movie-year">
                {new Date(movie.release_date).getFullYear()}
              </span>
              <span className="movie-rating">
                <svg className="star-icon" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                {movie.vote_average?.toFixed(1)}/10
              </span>
              <span className="movie-runtime">
                {formatRuntime(movie.runtime)}
              </span>
            </div>

            {movie.genres && movie.genres.length > 0 && (
              <div className="movie-genres mb-3">
                {movie.genres.map((genre) => (
                  <span key={genre.id} className="genre-tag">
                    {genre.name}
                  </span>
                ))}
              </div>
            )}

            <p className="movie-overview">{movie.overview}</p>

            {/* Action Buttons */}
            <div className="hero-buttons mb-4">
              {videos.length > 0 && (
                <a
                  href={`https://www.youtube.com/watch?v=${videos[0].key}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-primary"
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                  Watch Trailer
                </a>
              )}
              <button className="btn btn-secondary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
                Add to Watchlist
              </button>
            </div>

            {/* Movie Stats */}
            <div className="movie-stats">
              <div className="stat-item">
                <span className="stat-value">{movie.vote_count}</span>
                <span className="stat-label">Votes</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{movie.popularity?.toFixed(0)}</span>
                <span className="stat-label">Popularity</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{formatCurrency(movie.budget)}</span>
                <span className="stat-label">Budget</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{formatCurrency(movie.revenue)}</span>
                <span className="stat-label">Revenue</span>
              </div>
            </div>
          </div>
        </div>

        {/* Cast Section */}
        {cast.length > 0 && (
          <section className="section">
            <h2 className="section-title">Cast</h2>
            <div className="cast-grid">
              {cast.map((person) => (
                <CastCard key={person.id} person={person} />
              ))}
            </div>
          </section>
        )}

        {/* Crew Section */}
        {crew.length > 0 && (
          <section className="section">
            <h2 className="section-title">Crew</h2>
            <div className="crew-list">
              {crew.map((person, index) => (
                <div key={`${person.id}-${index}`} className="crew-item">
                  <span className="crew-name">{person.name}</span>
                  <span className="crew-job">{person.job}</span>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Videos Section */}
        {videos.length > 0 && (
          <section className="section">
            <h2 className="section-title">Videos</h2>
            <div className="videos-grid">
              {videos.map((video) => (
                <div key={video.id} className="video-item">
                  <iframe
                    src={`https://www.youtube.com/embed/${video.key}`}
                    title={video.name}
                    style={{ border: 'none' }}
                    allowFullScreen
                    className="video-iframe"
                  ></iframe>
                  <h4 className="video-title">{video.name}</h4>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Similar Movies Section */}
        {similarMovies.length > 0 && (
          <section className="section">
            <h2 className="section-title">Similar Movies</h2>
            <div className="movie-list">
              {similarMovies.map((movie) => (
                <SimilarMovieCard key={movie.id} movie={movie} />
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
}

export default MovieDetails;