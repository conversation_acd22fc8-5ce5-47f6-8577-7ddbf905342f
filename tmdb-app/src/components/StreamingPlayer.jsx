import React from 'react';
import { useNavigate } from 'react-router-dom';

const StreamingPlayer = ({ movieId, imdbId, title, type = 'movie', season = null, episode = null }) => {
  const navigate = useNavigate();

  const handlePlayClick = () => {
    // Construct the play page URL with query parameters
    const params = new URLSearchParams({
      type: type,
      title: title,
      ...(imdbId && { imdb: imdbId }),
      ...(type === 'tv' && season && { season: season }),
      ...(type === 'tv' && episode && { episode: episode })
    });

    navigate(`/play/${movieId}?${params.toString()}`);
  };

  return (
    <button
      className="btn btn-primary"
      onClick={handlePlayClick}
      aria-label={`Watch ${title}`}
    >
      <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M8 5v14l11-7z"/>
      </svg>
      {type === 'tv' ? `Watch S${season}E${episode}` : 'Watch Now'}
    </button>
  );
};

export default StreamingPlayer;
