body {
  background-color: #141414;
  color: #fff;
  font-family: 'Arial', sans-serif;
  margin: 0;
}

#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #e50914;
  margin-bottom: 20px;
  text-align: left;
}

nav {
  background-color: #141414;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

nav a {
  color: #fff;
  text-decoration: none;
  margin-right: 20px;
}

nav a:first-child {
  color: #e50914;
  font-size: 24px;
  font-weight: bold;
}

.movie-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  overflow-x: auto;
  padding: 10px;
}

.movie-card {
  width: 200px;
  margin: 10px;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease-in-out;
}

.movie-card:hover {
  transform: scale(1.05);
}

.movie-card a {
  text-decoration: none;
  color: #fff;
  display: block;
}

.movie-card img {
  width: 100%;
  height: auto;
  display: block;
}

.movie-card p {
  padding: 10px;
  margin: 0;
  font-weight: bold;
  text-align: left;
}
