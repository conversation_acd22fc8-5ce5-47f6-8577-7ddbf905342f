/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Variables for consistent theming */
:root {
  --primary-red: #e50914;
  --primary-red-dark: #b20710;
  --primary-red-light: #ff1e2d;
  --dark-bg: #0a0a0a;
  --dark-bg-secondary: #141414;
  --dark-bg-tertiary: #1a1a1a;
  --dark-bg-card: #1f1f1f;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #8c8c8c;
  --accent-gold: #ffd700;
  --success-green: #46d369;
  --warning-orange: #ff9500;
  --border-color: #333333;
  --border-color-light: #444444;
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.5);
  --shadow-heavy: rgba(0, 0, 0, 0.8);
  --gradient-primary: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-red-dark) 100%);
  --gradient-dark: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-bg-secondary) 100%);
  --gradient-overlay: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--dark-bg);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(229, 9, 20, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(229, 9, 20, 0.05) 0%, transparent 50%);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  min-height: 100vh;
}

#root {
  min-height: 100vh;
  position: relative;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
  position: relative;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

h2 {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  position: relative;
}

h3 {
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  color: var(--text-primary);
  margin-bottom: 1rem;
}

/* Container and Layout */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
  box-sizing: border-box;
}

@media (min-width: 576px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }
}

.section {
  margin-bottom: 4rem;
  position: relative;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 2px;
}

/* Navigation Styles */
nav {
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

nav.scrolled {
  background: rgba(10, 10, 10, 0.98);
  box-shadow: 0 8px 32px var(--shadow-medium);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.nav-logo {
  font-family: 'Poppins', sans-serif;
  font-size: 2rem;
  font-weight: 900;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 10px rgba(229, 9, 20, 0.5));
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transition: left 0.3s ease;
  z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
  left: 0;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
  transform: translateY(-2px);
}

/* Search Styles */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-form {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: 25px;
  padding: 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.search-form:focus-within {
  border-color: var(--primary-red);
  box-shadow: 0 0 20px rgba(229, 9, 20, 0.3);
  transform: scale(1.02);
}

.search-input {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
  padding: 0.5rem 1rem;
  width: 250px;
  outline: none;
  transition: width 0.3s ease;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-input:focus {
  width: 300px;
}

.search-button {
  background: var(--gradient-primary);
  border: none;
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.search-button:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
}

/* Search Results Dropdown */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--dark-bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 10px 30px var(--shadow-heavy);
  backdrop-filter: blur(20px);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 0.5rem;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--border-color);
}

.search-result-item:hover {
  background: var(--dark-bg-tertiary);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-poster {
  width: 40px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  margin-right: 1rem;
  flex-shrink: 0;
}

.search-result-info {
  flex: 1;
  min-width: 0;
}

.search-result-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.search-result-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: var(--text-muted);
}

.search-result-type {
  background: var(--primary-red);
  color: var(--text-primary);
  padding: 0.1rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.search-result-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: var(--dark-bg-secondary);
}

.search-view-all {
  width: 100%;
  background: transparent;
  border: 1px solid var(--primary-red);
  color: var(--primary-red);
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.search-view-all:hover {
  background: var(--primary-red);
  color: var(--text-primary);
}

.search-no-results {
  padding: 2rem;
  text-align: center;
  color: var(--text-muted);
}

/* Mobile Menu */
.mobile-menu-button {
  display: none;
  background: transparent;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.mobile-menu-button:hover {
  background: var(--dark-bg-tertiary);
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--dark-bg-card);
  border-top: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  z-index: 999;
}

.mobile-nav-link {
  display: block;
  padding: 1rem 2rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  background: var(--dark-bg-tertiary);
  color: var(--primary-red);
}

.mobile-nav-link:last-child {
  border-bottom: none;
}

/* Search Results Page */
.search-results-header {
  margin-bottom: 3rem;
  text-align: center;
}

.search-results-header h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
}

.search-results-count {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin: 0;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.no-results-content {
  text-align: center;
  max-width: 500px;
}

.no-results-content h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.no-results-content p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin: 4rem 0;
  padding: 2rem 0;
}

.pagination-btn {
  background: var(--gradient-primary);
  color: var(--text-primary);
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.pagination-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
}

.pagination-btn:disabled {
  background: var(--dark-bg-tertiary);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pagination-info {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  padding: 1rem 2rem;
  background: var(--dark-bg-card);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

/* Error Message */
.error-message {
  padding: 4rem 2rem;
  text-align: center;
}

.error-message h2 {
  color: var(--primary-red);
  margin-bottom: 1rem;
}

.error-message p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

/* Crew Styles */
.crew-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.crew-item {
  background: var(--dark-bg-card);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.crew-name {
  font-weight: 600;
  color: var(--text-primary);
}

.crew-job {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* Videos Styles */
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.video-item {
  background: var(--dark-bg-card);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.video-iframe {
  width: 100%;
  height: 200px;
  display: block;
}

.video-title {
  padding: 1rem;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Additional Movie Details Styles */
.movie-details .hero-section {
  margin-bottom: 2rem;
}

.movie-details .movie-header {
  margin-top: -100px;
  position: relative;
  z-index: 2;
}

.movie-details .movie-poster-large {
  box-shadow: 0 20px 40px var(--shadow-heavy);
  border: 4px solid var(--dark-bg);
}

.movie-runtime {
  background: var(--dark-bg-tertiary);
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.9rem;
  border: 1px solid var(--border-color);
}

/* Enhanced responsive design for movie details */
@media (max-width: 768px) {
  .movie-details .movie-header {
    margin-top: -50px;
  }

  .videos-grid {
    grid-template-columns: 1fr;
  }

  .crew-list {
    grid-template-columns: 1fr;
  }

  .movie-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Movies and Series Page Styles */
.movies-header {
  text-align: center;
  margin-bottom: 3rem;
}

.movies-header h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  margin-bottom: 1rem;
}

.movies-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Filters Container */
.filters-container {
  background: var(--dark-bg-card);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 3rem;
  border: 1px solid var(--border-color);
}

.filter-group {
  margin-bottom: 2rem;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.filter-btn {
  background: var(--dark-bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-btn:hover {
  background: var(--dark-bg-secondary);
  color: var(--text-primary);
  transform: translateY(-2px);
}

.filter-btn.active {
  background: var(--gradient-primary);
  color: var(--text-primary);
  border-color: var(--primary-red);
  box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
}

.sort-select {
  background: var(--dark-bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.sort-select:focus {
  outline: none;
  border-color: var(--primary-red);
  box-shadow: 0 0 0 2px rgba(229, 9, 20, 0.2);
}

.sort-select option {
  background: var(--dark-bg-card);
  color: var(--text-primary);
}

/* Enhanced responsive design for filters */
@media (max-width: 768px) {
  .filters-container {
    padding: 1.5rem;
  }

  .filter-buttons {
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .sort-select {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .movies-header h1 {
    font-size: 2rem;
  }

  .movies-subtitle {
    font-size: 1rem;
  }

  .filters-container {
    padding: 1rem;
  }

  .filter-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

/* Movie Grid and Card Styles */
.movie-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 1rem;
  padding: 1rem 0;
}

@media (min-width: 480px) {
  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 768px) {
  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }
}

.movie-list {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 1rem 0;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-red) var(--dark-bg-secondary);
  -webkit-overflow-scrolling: touch;
}

@media (min-width: 768px) {
  .movie-list {
    gap: 1.5rem;
  }
}

.movie-list::-webkit-scrollbar {
  height: 8px;
}

.movie-list::-webkit-scrollbar-track {
  background: var(--dark-bg-secondary);
  border-radius: 4px;
}

.movie-list::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

.movie-list::-webkit-scrollbar-thumb:hover {
  background: var(--primary-red-light);
}

.movie-card {
  min-width: 160px;
  width: 100%;
  background: var(--dark-bg-card);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  cursor: pointer;
  border: 1px solid transparent;
  backdrop-filter: blur(10px);
}

@media (min-width: 480px) {
  .movie-card {
    min-width: 200px;
    border-radius: 14px;
  }
}

@media (min-width: 768px) {
  .movie-card {
    min-width: 240px;
    border-radius: 16px;
  }
}

@media (min-width: 1024px) {
  .movie-card {
    min-width: 280px;
  }
}

.movie-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: 16px;
}

.movie-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 20px 40px var(--shadow-heavy),
    0 0 0 1px rgba(229, 9, 20, 0.3);
  border-color: var(--primary-red);
}

.movie-card:hover::before {
  opacity: 0.1;
}

.movie-card a {
  text-decoration: none;
  color: inherit;
  display: block;
  height: 100%;
}

.movie-poster-container {
  position: relative;
  overflow: hidden;
  aspect-ratio: 2/3;
}

.movie-poster {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.movie-card:hover .movie-poster {
  transform: scale(1.1);
}

.movie-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-overlay);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.movie-card:hover .movie-overlay {
  opacity: 1;
}

.play-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.play-button:hover {
  background: var(--text-primary);
  transform: scale(1.1);
}

.play-icon {
  width: 24px;
  height: 24px;
  fill: var(--dark-bg);
  margin-left: 2px;
}

.movie-info {
  padding: 1.5rem;
  background: var(--dark-bg-card);
}

.movie-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.movie-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.movie-year {
  color: var(--text-muted);
}

.movie-rating {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: var(--accent-gold);
  font-weight: 600;
}

.star-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.movie-genres {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.genre-tag {
  background: rgba(229, 9, 20, 0.2);
  color: var(--primary-red-light);
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(229, 9, 20, 0.3);
}

/* Hero Section Styles */
.hero-section {
  position: relative;
  height: 80vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  overflow: hidden;
  margin-bottom: 4rem;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(10, 10, 10, 0.9) 0%,
    rgba(10, 10, 10, 0.7) 50%,
    transparent 100%
  );
  z-index: -1;
}

.hero-content {
  max-width: 600px;
  padding: 0 2rem;
  z-index: 1;
}

.hero-title {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 900;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px var(--shadow-heavy);
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
  text-shadow: 1px 1px 2px var(--shadow-medium);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Loading States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-red);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.skeleton {
  background: linear-gradient(90deg, var(--dark-bg-secondary) 25%, var(--dark-bg-tertiary) 50%, var(--dark-bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-card {
  background: var(--dark-bg-card);
  border-radius: 16px;
  overflow: hidden;
  min-width: 280px;
}

.skeleton-poster {
  width: 100%;
  aspect-ratio: 2/3;
}

.skeleton-info {
  padding: 1.5rem;
}

.skeleton-title {
  height: 20px;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.skeleton-meta {
  height: 16px;
  border-radius: 4px;
  width: 60%;
}

/* Movie Details Styles */
.movie-details {
  padding: 2rem 0;
}

.movie-header {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.movie-poster-large {
  width: 100%;
  border-radius: 16px;
  box-shadow: 0 20px 40px var(--shadow-heavy);
}

.movie-details-info h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
}

.movie-tagline {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: 1rem;
}

.movie-overview {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.movie-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-item {
  background: var(--dark-bg-card);
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  border: 1px solid var(--border-color);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-red);
  display: block;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-top: 0.5rem;
}

/* Cast and Crew Styles */
.cast-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.cast-card {
  background: var(--dark-bg-card);
  border-radius: 12px;
  overflow: hidden;
  text-align: center;
  transition: transform 0.3s ease;
  border: 1px solid var(--border-color);
}

.cast-card:hover {
  transform: translateY(-5px);
}

.cast-photo {
  width: 100%;
  aspect-ratio: 2/3;
  object-fit: cover;
}

.cast-info {
  padding: 1rem;
}

.cast-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.cast-character {
  color: var(--text-muted);
  font-size: 0.8rem;
}

/* Enhanced Responsive Design */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: 1400px;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }

  .hero-content {
    max-width: 700px;
  }
}

/* Medium-large screens (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
  .container {
    padding: 0 1.5rem;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1.8rem;
  }

  .nav-container {
    padding: 0 1.5rem;
  }
}

/* Medium screens (768px to 991px) */
@media (max-width: 991px) and (min-width: 768px) {
  .container {
    padding: 0 1.5rem;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1.5rem;
  }

  .hero-section {
    height: 70vh;
    min-height: 500px;
  }

  .hero-content {
    max-width: 500px;
  }

  .hero-title {
    font-size: clamp(2rem, 5vw, 3rem);
  }

  .hero-description {
    font-size: 1.1rem;
  }
}

/* Small screens (576px to 767px) */
@media (max-width: 767px) and (min-width: 576px) {
  .container {
    padding: 0 1rem;
  }

  .nav-container {
    padding: 0 1rem;
    position: relative;
  }

  .nav-links {
    display: none;
  }

  .mobile-menu-button {
    display: block;
  }

  .mobile-menu {
    display: block;
  }

  .search-container {
    order: -1;
    margin-right: auto;
  }

  .search-input {
    width: 160px;
    font-size: 0.9rem;
  }

  .search-input:focus {
    width: 200px;
  }

  .search-button {
    padding: 0.4rem 0.8rem;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .movie-card {
    min-width: 200px;
  }

  .hero-section {
    height: 60vh;
    min-height: 400px;
  }

  .hero-content {
    padding: 0 1rem;
    max-width: 400px;
  }

  .hero-title {
    font-size: clamp(1.8rem, 6vw, 2.5rem);
  }

  .hero-description {
    font-size: 1rem;
    line-height: 1.5;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .btn {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
    justify-content: center;
  }

  .movie-header {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    text-align: center;
  }

  .movie-poster-large {
    max-width: 250px;
    margin: 0 auto;
  }

  .movie-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .cast-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
  }

  .videos-grid {
    grid-template-columns: 1fr;
  }

  .crew-list {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .section-title::before {
    left: -15px;
    width: 3px;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .movie-card {
    min-width: 200px;
  }

  .movie-header {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .movie-poster-large {
    max-width: 300px;
    margin: 0 auto;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .hero-buttons {
    justify-content: center;
  }

  .cast-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
  }
}

/* Extra small screens (up to 575px) */
@media (max-width: 575px) {
  .container {
    padding: 0 0.75rem;
  }

  .nav-container {
    flex-wrap: wrap;
    gap: 0.75rem;
    padding: 0 0.75rem;
  }

  .nav-logo {
    font-size: 1.5rem;
  }

  .search-container {
    width: 100%;
    order: 2;
  }

  .search-form {
    width: 100%;
  }

  .search-input {
    width: 100%;
    font-size: 0.9rem;
  }

  .search-input:focus {
    width: 100%;
  }

  .mobile-menu-button {
    order: 1;
    margin-left: auto;
  }

  .movie-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.75rem;
  }

  .movie-card {
    min-width: 160px;
  }

  .movie-list {
    gap: 1rem;
  }

  .hero-section {
    height: 50vh;
    min-height: 350px;
  }

  .hero-content {
    padding: 0 0.75rem;
  }

  .hero-title {
    font-size: clamp(1.5rem, 8vw, 2rem);
    margin-bottom: 0.75rem;
  }

  .hero-description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .btn {
    padding: 0.7rem 1.2rem;
    font-size: 0.85rem;
  }

  .movies-header h1 {
    font-size: 2rem;
  }

  .movies-subtitle {
    font-size: 0.95rem;
  }

  .filters-container {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .filter-title {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .sort-select {
    width: 100%;
    font-size: 0.9rem;
  }

  .movie-header {
    margin-top: -30px;
  }

  .movie-poster-large {
    max-width: 200px;
  }

  .movie-details-info h1 {
    font-size: 1.8rem;
  }

  .movie-tagline {
    font-size: 1rem;
  }

  .movie-overview {
    font-size: 1rem;
  }

  .movie-stats {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .stat-item {
    padding: 0.75rem;
  }

  .stat-value {
    font-size: 1.2rem;
  }

  .cast-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
  }

  .cast-card {
    border-radius: 8px;
  }

  .cast-info {
    padding: 0.75rem;
  }

  .cast-name {
    font-size: 0.8rem;
  }

  .cast-character {
    font-size: 0.75rem;
  }

  .section {
    margin-bottom: 2.5rem;
  }

  .section-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
  }

  .section-title::before {
    left: -10px;
    width: 2px;
  }

  .pagination {
    flex-direction: column;
    gap: 1rem;
    margin: 2rem 0;
  }

  .pagination-btn {
    min-width: 100px;
    padding: 0.8rem 1.5rem;
  }

  .pagination-info {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .search-results-header h1 {
    font-size: 1.8rem;
  }

  .search-results-count {
    font-size: 1rem;
  }

  .no-results-content h2 {
    font-size: 1.5rem;
  }

  .no-results-content p {
    font-size: 0.9rem;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }

.hidden { display: none; }
.visible { display: block; }

/* Touch and Mobile Interactions */
@media (hover: none) and (pointer: coarse) {
  /* Touch devices */
  .movie-card:hover {
    transform: none;
  }

  .movie-card:active {
    transform: scale(0.98);
  }

  .btn:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.98);
  }

  .filter-btn:hover {
    transform: none;
  }

  .filter-btn:active {
    transform: scale(0.98);
  }

  .nav-link:hover {
    transform: none;
  }

  .nav-link:active {
    background: var(--primary-red);
  }
}

/* Enhanced Focus and Accessibility */
*:focus {
  outline: 2px solid var(--primary-red);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary-red);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-muted: #cccccc;
    --border-color: #666666;
    --border-color-light: #888888;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .movie-card {
    transition: none;
  }

  .btn {
    transition: none;
  }

  .nav-link {
    transition: none;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to main content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-red);
  color: var(--text-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* Keyboard navigation improvements */
.movie-card:focus-within {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px var(--shadow-heavy);
  border-color: var(--primary-red);
}

.filter-btn:focus,
.pagination-btn:focus,
.search-button:focus {
  outline: 2px solid var(--primary-red);
  outline-offset: 2px;
}

/* Better button states for accessibility */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

button:disabled:focus {
  outline: 2px solid var(--text-muted);
}

/* Loading state accessibility */
.loading-container[aria-live="polite"] {
  position: relative;
}

.loading-container::after {
  content: "Loading content, please wait...";
  position: absolute;
  left: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Error state accessibility */
.error-message[role="alert"] {
  border: 2px solid var(--primary-red);
  border-radius: 8px;
  padding: 2rem;
}

/* Form accessibility improvements */
.search-input:invalid {
  border-color: var(--warning-orange);
}

.search-input:valid {
  border-color: var(--success-green);
}

/* Better contrast for genre tags */
.genre-tag {
  background: rgba(229, 9, 20, 0.3);
  border: 1px solid rgba(229, 9, 20, 0.6);
}

/* Improved mobile menu accessibility */
.mobile-menu-button[aria-expanded="true"] {
  background: var(--primary-red);
}

.mobile-menu[aria-hidden="false"] {
  display: block;
}

.mobile-menu[aria-hidden="true"] {
  display: none;
}

/* Better focus indicators for mobile menu */
.mobile-nav-link:focus {
  background: var(--primary-red);
  color: var(--text-primary);
  outline: none;
}

/* Improved search results accessibility */
.search-result-item:focus {
  background: var(--primary-red);
  color: var(--text-primary);
  outline: none;
}

.search-result-item[aria-selected="true"] {
  background: var(--primary-red);
  color: var(--text-primary);
}

/* Streaming Player Styles */
.streaming-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.3s ease-out;
}

.streaming-modal-content {
  background: var(--dark-bg-card);
  border-radius: 16px;
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: 0 20px 60px var(--shadow-heavy);
  animation: fadeInUp 0.3s ease-out;
}

.streaming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--dark-bg-secondary);
}

.streaming-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.streaming-close {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.streaming-close:hover {
  background: var(--primary-red);
  color: var(--text-primary);
}

.streaming-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: var(--dark-bg);
}

.streaming-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.streaming-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-secondary);
}

.streaming-loading .spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
}

.streaming-info {
  padding: 1rem 2rem;
  background: var(--dark-bg-tertiary);
  border-top: 1px solid var(--border-color);
}

.streaming-disclaimer {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin: 0;
  text-align: center;
}

/* Responsive streaming player */
@media (max-width: 768px) {
  .streaming-modal-content {
    width: 98%;
    max-height: 95vh;
  }

  .streaming-header {
    padding: 1rem 1.5rem;
  }

  .streaming-title {
    font-size: 1rem;
  }

  .streaming-info {
    padding: 1rem 1.5rem;
  }

  .streaming-disclaimer {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .streaming-modal-content {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .streaming-header {
    padding: 1rem;
  }

  .streaming-title {
    font-size: 0.9rem;
  }

  .streaming-player-container {
    padding-bottom: 75%; /* Adjust aspect ratio for mobile */
  }
}

/* Streaming Section Styles */
.streaming-section {
  background: var(--dark-bg-card);
  border-radius: 12px;
  padding: 2rem;
  border: 1px solid var(--border-color);
  text-align: center;
}

.streaming-description {
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.streaming-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .streaming-section {
    padding: 1.5rem;
  }

  .streaming-description {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .streaming-buttons {
    flex-direction: column;
    align-items: center;
  }
}

/* Play Page Styles */
.play-page {
  min-height: 100vh;
  background: var(--dark-bg);
}

/* Debug Info Styles */
.debug-info {
  background: #333 !important;
  padding: 1rem !important;
  border-radius: 8px !important;
  font-size: 0.8rem !important;
  margin-bottom: 1rem !important;
  border: 1px solid #555;
}

.debug-info h4 {
  color: #fff;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.debug-info p {
  margin: 0.25rem 0;
  color: #ccc;
  word-break: break-all;
}

.debug-info button {
  background: #e50914 !important;
  color: white !important;
  border: none !important;
  padding: 0.5rem 1rem !important;
  border-radius: 4px !important;
  margin-top: 0.5rem !important;
  cursor: pointer !important;
  font-size: 0.8rem !important;
}

.debug-info button:hover {
  background: #b8070f !important;
}

.play-header {
  background: var(--dark-bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.play-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.play-back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: var(--dark-bg-tertiary);
  border: 1px solid var(--border-color);
}

.play-back-btn:hover {
  color: var(--text-primary);
  background: var(--primary-red);
  border-color: var(--primary-red);
}

.play-title {
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.play-content {
  padding: 2rem 0;
}

.play-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 2rem;
  align-items: start;
}

.play-player-section {
  background: var(--dark-bg-card);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.play-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: var(--dark-bg);
}

.play-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.play-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-secondary);
}

.play-loading .spinner {
  width: 50px;
  height: 50px;
  margin-bottom: 1rem;
}

.play-sidebar {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.server-selection {
  background: var(--dark-bg-card);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.server-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.server-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.server-btn {
  background: var(--dark-bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: left;
}

.server-btn:hover {
  background: var(--dark-bg-secondary);
  border-color: var(--primary-red);
  transform: translateY(-2px);
}

.server-btn.active {
  background: var(--gradient-primary);
  border-color: var(--primary-red);
  box-shadow: 0 4px 15px rgba(229, 9, 20, 0.3);
}

.server-info {
  flex: 1;
}

.server-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.server-desc {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.server-indicator {
  color: var(--text-primary);
  margin-left: 1rem;
}

.content-info {
  background: var(--dark-bg-card);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.content-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.content-details {
  display: flex;
  gap: 1rem;
}

.content-poster {
  width: 80px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.content-meta {
  flex: 1;
}

.content-overview {
  font-size: 0.9rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.content-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.play-error {
  text-align: center;
  padding: 4rem 2rem;
}

.play-error h2 {
  color: var(--primary-red);
  margin-bottom: 1rem;
}

.play-error p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

/* Play Page Responsive Design */
@media (max-width: 1024px) {
  .play-layout {
    grid-template-columns: 1fr 300px;
    gap: 1.5rem;
  }

  .server-selection,
  .content-info {
    padding: 1.25rem;
  }
}

@media (max-width: 768px) {
  .play-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .play-content {
    padding: 1rem 0;
  }

  .play-nav {
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .play-title {
    font-size: 1.2rem;
    width: 100%;
    order: 2;
  }

  .play-back-btn {
    order: 1;
  }

  .play-sidebar {
    order: -1;
  }

  .server-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
  }

  .content-details {
    flex-direction: column;
    text-align: center;
  }

  .content-poster {
    width: 120px;
    height: 180px;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .play-header {
    padding: 0.75rem 0;
  }

  .play-nav {
    gap: 0.5rem;
  }

  .play-back-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .play-title {
    font-size: 1rem;
  }

  .play-content {
    padding: 0.75rem 0;
  }

  .play-player-container {
    padding-bottom: 75%; /* Adjust aspect ratio for mobile */
  }

  .server-selection,
  .content-info {
    padding: 1rem;
    border-radius: 12px;
  }

  .server-list {
    grid-template-columns: 1fr;
  }

  .server-btn {
    padding: 0.75rem;
  }

  .server-name {
    font-size: 0.9rem;
  }

  .server-desc {
    font-size: 0.8rem;
  }

  .content-poster {
    width: 100px;
    height: 150px;
  }

  .content-overview {
    font-size: 0.85rem;
  }
}
