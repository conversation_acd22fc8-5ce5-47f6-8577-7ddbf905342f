{% extends "base.html" %}

{% block title %}Error - CineStream{% endblock %}

{% block extra_css %}
<style>
    .error-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 2rem;
    }
    
    .error-content {
        background: var(--dark-secondary);
        border-radius: 16px;
        padding: 3rem;
        max-width: 600px;
        border: 1px solid #333;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
    
    .error-icon {
        font-size: 4rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
    }
    
    .error-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }
    
    .error-message {
        font-size: 1.1rem;
        color: var(--text-secondary);
        margin-bottom: 2rem;
        line-height: 1.6;
    }
    
    .error-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .error-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .error-btn-primary {
        background: var(--primary-color);
        color: var(--text-primary);
        border: 1px solid var(--primary-color);
    }
    
    .error-btn-primary:hover {
        background: var(--primary-dark);
        color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(229, 9, 20, 0.4);
    }
    
    .error-btn-secondary {
        background: var(--dark-tertiary);
        color: var(--text-secondary);
        border: 1px solid #333;
    }
    
    .error-btn-secondary:hover {
        background: var(--dark-bg);
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .error-content {
            padding: 2rem;
            margin: 1rem;
        }
        
        .error-title {
            font-size: 1.5rem;
        }
        
        .error-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-title">Oops! Something went wrong</h1>
        
        <p class="error-message">
            {{ error if error else "We're sorry, but something unexpected happened. Please try again later." }}
        </p>
        
        <div class="error-actions">
            <a href="{{ url_for('home') }}" class="error-btn error-btn-primary">
                <i class="fas fa-home"></i>
                Go Home
            </a>
            <a href="javascript:history.back()" class="error-btn error-btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Go Back
            </a>
            <a href="javascript:location.reload()" class="error-btn error-btn-secondary">
                <i class="fas fa-redo"></i>
                Refresh Page
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh after 30 seconds for server errors
if (window.location.search.includes('500') || '{{ error }}'.includes('server')) {
    setTimeout(function() {
        if (confirm('Would you like to refresh the page and try again?')) {
            location.reload();
        }
    }, 30000);
}

// Add keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        history.back();
    } else if (e.key === 'Enter' && e.ctrlKey) {
        location.reload();
    }
});
</script>
{% endblock %}
