{% extends "base.html" %}

{% block title %}{{ show.name }} ({{ show.first_air_date[:4] if show.first_air_date else 'N/A' }}) - CineStream{% endblock %}

{% block extra_css %}
<style>
    .hero-backdrop {
        position: relative;
        height: 60vh;
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
        display: flex;
        align-items: end;
    }
    
    .hero-backdrop::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, transparent 0%, rgba(0,0,0,0.8) 100%);
    }
    
    .hero-content {
        position: relative;
        z-index: 2;
        color: white;
        padding: 2rem 0;
    }
    
    .show-poster-large {
        width: 100%;
        max-width: 300px;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.5);
    }
    
    .show-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    }
    
    .show-tagline {
        font-size: 1.2rem;
        font-style: italic;
        color: #ccc;
        margin-bottom: 1rem;
    }
    
    .show-meta {
        display: flex;
        gap: 2rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1rem;
    }
    
    .rating-badge {
        background: #2196F3;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
    }
    
    .genre-tags {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-bottom: 2rem;
    }
    
    .genre-tag {
        background: rgba(255,255,255,0.1);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 15px;
        font-size: 0.9rem;
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .show-overview {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #ddd;
        margin-bottom: 2rem;
    }
    
    .watch-btn {
        background: linear-gradient(135deg, #2196F3, #1976D2);
        border: none;
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .watch-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(33, 150, 243, 0.4);
        color: white;
    }
    
    .details-section {
        background: var(--dark-bg);
        padding: 3rem 0;
    }
    
    .cast-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }
    
    .cast-card {
        background: var(--dark-secondary);
        border-radius: 12px;
        overflow: hidden;
        text-align: center;
        transition: transform 0.3s ease;
        border: 1px solid #333;
    }
    
    .cast-card:hover {
        transform: translateY(-5px);
    }
    
    .cast-photo {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    .cast-info {
        padding: 1rem;
    }
    
    .cast-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.3rem;
        font-size: 0.9rem;
    }
    
    .cast-character {
        color: var(--text-muted);
        font-size: 0.8rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }
    
    .stat-card {
        background: var(--dark-secondary);
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        border: 1px solid #333;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2196F3;
        display: block;
    }
    
    .stat-label {
        color: var(--text-muted);
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }
    
    .seasons-info {
        background: var(--dark-secondary);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        border: 1px solid #333;
    }
    
    .seasons-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }
    
    .season-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #333;
    }
    
    .season-item:last-child {
        border-bottom: none;
    }
    
    .season-name {
        font-weight: 500;
        color: var(--text-primary);
    }
    
    .season-episodes {
        color: var(--text-muted);
        font-size: 0.9rem;
    }
    
    @media (max-width: 768px) {
        .hero-backdrop {
            height: 40vh;
        }
        
        .show-title {
            font-size: 1.8rem;
        }
        
        .show-meta {
            flex-direction: column;
            gap: 1rem;
        }
        
        .cast-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
        }
        
        .cast-photo {
            height: 150px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section with Backdrop -->
{% if show.backdrop_path %}
<section class="hero-backdrop" style="background-image: url('https://image.tmdb.org/t/p/original{{ show.backdrop_path }}');">
{% else %}
<section class="hero-backdrop" style="background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 100%);">
{% endif %}
    <div class="container">
        <div class="hero-content">
            <div class="row align-items-end">
                <div class="col-md-3">
                    <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}" 
                         alt="{{ show.name }}" 
                         class="show-poster-large"
                         onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                </div>
                <div class="col-md-9">
                    <h1 class="show-title">{{ show.name }}</h1>
                    
                    {% if show.tagline %}
                    <p class="show-tagline">"{{ show.tagline }}"</p>
                    {% endif %}
                    
                    <div class="show-meta">
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ show.first_air_date if show.first_air_date else 'N/A' }}</span>
                        </div>
                        {% if show.number_of_seasons %}
                        <div class="meta-item">
                            <i class="fas fa-list"></i>
                            <span>{{ show.number_of_seasons }} Season{{ 's' if show.number_of_seasons != 1 else '' }}</span>
                        </div>
                        {% endif %}
                        {% if show.number_of_episodes %}
                        <div class="meta-item">
                            <i class="fas fa-play-circle"></i>
                            <span>{{ show.number_of_episodes }} Episodes</span>
                        </div>
                        {% endif %}
                        <div class="meta-item">
                            <div class="rating-badge">
                                <i class="fas fa-star"></i> {{ "%.1f"|format(show.vote_average) }}
                            </div>
                        </div>
                    </div>
                    
                    {% if show.genres %}
                    <div class="genre-tags">
                        {% for genre in show.genres %}
                        <span class="genre-tag">{{ genre.name }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    {% if show.overview %}
                    <p class="show-overview">{{ show.overview }}</p>
                    {% endif %}
                    
                    <a href="{{ url_for('watch', media_type='tv', tmdb_id=show.id, season=1, episode=1) }}" class="watch-btn">
                        <i class="fas fa-play"></i>
                        Watch S1E1
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Details Section -->
<section class="details-section">
    <div class="container">
        <!-- Show Stats -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-value">{{ "%.1f"|format(show.vote_average) }}</span>
                <div class="stat-label">Rating</div>
            </div>
            <div class="stat-card">
                <span class="stat-value">{{ show.vote_count }}</span>
                <div class="stat-label">Votes</div>
            </div>
            {% if show.number_of_seasons %}
            <div class="stat-card">
                <span class="stat-value">{{ show.number_of_seasons }}</span>
                <div class="stat-label">Seasons</div>
            </div>
            {% endif %}
            {% if show.number_of_episodes %}
            <div class="stat-card">
                <span class="stat-value">{{ show.number_of_episodes }}</span>
                <div class="stat-label">Episodes</div>
            </div>
            {% endif %}
        </div>
        
        <!-- Seasons Info -->
        {% if show.seasons %}
        <div class="seasons-info">
            <h3 class="seasons-title">
                <i class="fas fa-list me-2"></i>Seasons
            </h3>
            {% for season in show.seasons %}
            {% if season.season_number > 0 %}
            <div class="season-item">
                <div class="season-name">{{ season.name }}</div>
                <div class="season-episodes">{{ season.episode_count }} episodes</div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Cast Section -->
        {% if cast %}
        <div class="row mt-5">
            <div class="col-12">
                <h2 class="section-title mb-4">
                    <i class="fas fa-users me-2"></i>Cast
                </h2>
                <div class="cast-grid">
                    {% for person in cast %}
                    <div class="cast-card">
                        <img src="{% if person.profile_path %}https://image.tmdb.org/t/p/w300{{ person.profile_path }}{% else %}https://via.placeholder.com/300x400/333/fff?text=No+Photo{% endif %}" 
                             alt="{{ person.name }}" 
                             class="cast-photo">
                        <div class="cast-info">
                            <div class="cast-name">{{ person.name }}</div>
                            <div class="cast-character">{{ person.character }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Similar Shows -->
        {% if similar %}
        <div class="row mt-5">
            <div class="col-12">
                <h2 class="section-title mb-4">
                    <i class="fas fa-tv me-2"></i>Similar Shows
                </h2>
                <div class="row g-4">
                    {% for similar_show in similar %}
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                        <div class="movie-card">
                            <a href="{{ url_for('tv_details', tv_id=similar_show.id) }}" class="text-decoration-none">
                                <img src="https://image.tmdb.org/t/p/w500{{ similar_show.poster_path }}" 
                                     alt="{{ similar_show.name }}" 
                                     class="movie-poster"
                                     onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                                <div class="movie-info">
                                    <h5 class="movie-title">{{ similar_show.name }}</h5>
                                    <div class="movie-meta">
                                        <span>{{ similar_show.first_air_date[:4] if similar_show.first_air_date else 'N/A' }}</span>
                                        <span class="rating">
                                            <i class="fas fa-star"></i> {{ "%.1f"|format(similar_show.vote_average) }}
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Add active class to TV Shows nav item
document.addEventListener('DOMContentLoaded', function() {
    const tvLink = document.querySelector('a[href="{{ url_for("tv_shows") }}"]');
    if (tvLink) {
        tvLink.classList.add('active');
    }
});

// Smooth scroll for watch button
document.querySelector('.watch-btn').addEventListener('click', function(e) {
    // Add a small delay to show the click effect
    setTimeout(() => {
        window.location.href = this.href;
    }, 100);
});
</script>
{% endblock %}
