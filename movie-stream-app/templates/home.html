{% extends "base.html" %}

{% block title %}CineStream - Watch Movies & TV Shows Online{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <h1 class="hero-title">
            <i class="fas fa-play-circle me-3"></i>CineStream
        </h1>
        <p class="hero-subtitle">
            Watch unlimited movies and TV shows online. High quality streaming with multiple servers.
        </p>
        <div class="hero-buttons">
            <a href="{{ url_for('movies') }}" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-film me-2"></i>Browse Movies
            </a>
            <a href="{{ url_for('tv_shows') }}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-tv me-2"></i>Browse TV Shows
            </a>
        </div>
    </div>
</section>

<!-- Trending Movies -->
{% if trending_movies %}
<section class="py-5">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-fire text-danger me-2"></i>Trending Movies
            </h2>
            <a href="{{ url_for('movies') }}" class="btn btn-outline-light">View All</a>
        </div>
        
        <div class="row g-4">
            {% for movie in trending_movies %}
            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                <div class="movie-card">
                    <a href="{{ url_for('movie_details', movie_id=movie.id) }}" class="text-decoration-none">
                        <img src="https://image.tmdb.org/t/p/w500{{ movie.poster_path }}" 
                             alt="{{ movie.title }}" 
                             class="movie-poster"
                             onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                        <div class="movie-info">
                            <h5 class="movie-title">{{ movie.title }}</h5>
                            <div class="movie-meta">
                                <span>{{ movie.release_date[:4] if movie.release_date else 'N/A' }}</span>
                                <span class="rating">
                                    <i class="fas fa-star"></i> {{ "%.1f"|format(movie.vote_average) }}
                                </span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Popular Movies -->
{% if popular_movies %}
<section class="py-5">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-star text-warning me-2"></i>Popular Movies
            </h2>
            <a href="{{ url_for('movies') }}?category=popular" class="btn btn-outline-light">View All</a>
        </div>
        
        <div class="row g-4">
            {% for movie in popular_movies %}
            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                <div class="movie-card">
                    <a href="{{ url_for('movie_details', movie_id=movie.id) }}" class="text-decoration-none">
                        <img src="https://image.tmdb.org/t/p/w500{{ movie.poster_path }}" 
                             alt="{{ movie.title }}" 
                             class="movie-poster"
                             onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                        <div class="movie-info">
                            <h5 class="movie-title">{{ movie.title }}</h5>
                            <div class="movie-meta">
                                <span>{{ movie.release_date[:4] if movie.release_date else 'N/A' }}</span>
                                <span class="rating">
                                    <i class="fas fa-star"></i> {{ "%.1f"|format(movie.vote_average) }}
                                </span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Trending TV Shows -->
{% if trending_tv %}
<section class="py-5">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-tv text-info me-2"></i>Trending TV Shows
            </h2>
            <a href="{{ url_for('tv_shows') }}" class="btn btn-outline-light">View All</a>
        </div>
        
        <div class="row g-4">
            {% for show in trending_tv %}
            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                <div class="movie-card">
                    <a href="{{ url_for('tv_details', tv_id=show.id) }}" class="text-decoration-none">
                        <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}" 
                             alt="{{ show.name }}" 
                             class="movie-poster"
                             onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                        <div class="movie-info">
                            <h5 class="movie-title">{{ show.name }}</h5>
                            <div class="movie-meta">
                                <span>{{ show.first_air_date[:4] if show.first_air_date else 'N/A' }}</span>
                                <span class="rating">
                                    <i class="fas fa-star"></i> {{ "%.1f"|format(show.vote_average) }}
                                </span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Features Section -->
<section class="py-5" style="background: var(--dark-secondary);">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="feature-card p-4">
                    <i class="fas fa-hd-video fa-3x text-primary mb-3"></i>
                    <h4>High Quality</h4>
                    <p class="text-muted">Watch in HD quality with multiple streaming servers for the best experience.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-card p-4">
                    <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                    <h4>Any Device</h4>
                    <p class="text-muted">Stream on any device - desktop, tablet, or mobile. Responsive design for all screens.</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="feature-card p-4">
                    <i class="fas fa-infinity fa-3x text-primary mb-3"></i>
                    <h4>Unlimited</h4>
                    <p class="text-muted">Access thousands of movies and TV shows. New content added regularly.</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Add active class to current nav item
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
            behavior: 'smooth'
        });
    });
});
</script>
{% endblock %}
