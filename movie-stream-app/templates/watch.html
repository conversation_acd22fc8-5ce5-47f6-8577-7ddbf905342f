{% extends "base.html" %}

{% block title %}
Watch {{ content.title if media_type == 'movie' else content.name }} - CineStream
{% endblock %}

{% block extra_css %}
<style>
    .watch-container {
        background: var(--dark-bg);
        min-height: 100vh;
        padding-top: 2rem;
    }
    
    .player-container {
        background: var(--dark-secondary);
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #333;
        margin-bottom: 2rem;
    }
    
    .player-header {
        background: var(--dark-tertiary);
        padding: 1rem;
        border-bottom: 1px solid #333;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .player-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .back-btn {
        color: var(--text-secondary);
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        background: var(--dark-bg);
        border: 1px solid #333;
        transition: all 0.3s ease;
    }
    
    .back-btn:hover {
        color: var(--text-primary);
        background: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .player-wrapper {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        background: #000;
    }
    
    .player-iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: var(--text-primary);
    }
    
    .error-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: var(--text-primary);
        text-align: center;
        padding: 2rem;
    }
    
    .server-selection {
        background: var(--dark-secondary);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid #333;
    }
    
    .server-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }
    
    .server-btn {
        display: block;
        width: 100%;
        background: var(--dark-tertiary);
        border: 1px solid #333;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 0.75rem;
        color: var(--text-secondary);
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .server-btn:hover,
    .server-btn.active {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    .server-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }
    
    .server-desc {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .content-info {
        background: var(--dark-secondary);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid #333;
        margin-top: 1rem;
    }
    
    .content-poster {
        width: 100%;
        max-width: 200px;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .content-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }
    
    .content-meta {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: var(--text-muted);
    }
    
    .content-overview {
        color: var(--text-secondary);
        line-height: 1.6;
        font-size: 0.95rem;
    }
    
    .debug-panel {
        background: var(--dark-tertiary);
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        border: 1px solid #444;
    }
    
    .debug-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }
    
    .debug-info {
        font-size: 0.8rem;
        color: var(--text-muted);
        font-family: monospace;
        word-break: break-all;
    }
    
    .episode-selector {
        background: var(--dark-secondary);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid #333;
        margin-bottom: 1rem;
    }
    
    .episode-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .episode-input {
        background: var(--dark-tertiary);
        border: 1px solid #333;
        border-radius: 6px;
        color: var(--text-primary);
        padding: 0.5rem;
        width: 80px;
        text-align: center;
    }
    
    .episode-input:focus {
        border-color: var(--primary-color);
        outline: none;
    }
    
    @media (max-width: 768px) {
        .player-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
        
        .content-meta {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="watch-container">
    <div class="container">
        <div class="row">
            <!-- Main Player Column -->
            <div class="col-lg-8">
                <!-- Player Container -->
                <div class="player-container">
                    <div class="player-header">
                        <h1 class="player-title">
                            {{ content.title if media_type == 'movie' else content.name }}
                            {% if media_type == 'tv' %}
                                - S{{ season }}E{{ episode }}
                            {% endif %}
                        </h1>
                        <a href="{{ url_for('movie_details' if media_type == 'movie' else 'tv_details', 
                                          movie_id=tmdb_id if media_type == 'movie' else None,
                                          tv_id=tmdb_id if media_type == 'tv' else None) }}" 
                           class="back-btn">
                            <i class="fas fa-arrow-left me-2"></i>Back to Details
                        </a>
                    </div>
                    
                    <div class="player-wrapper">
                        <iframe id="player-iframe" 
                                class="player-iframe"
                                src="{{ streaming_urls[current_server] }}"
                                allowfullscreen
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share">
                        </iframe>
                        
                        <div id="loading-overlay" class="loading-overlay">
                            <div class="spinner"></div>
                            <p class="mt-3">Loading player...</p>
                        </div>
                        
                        <div id="error-overlay" class="error-overlay" style="display: none;">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h4>Streaming Error</h4>
                            <p>This server is currently unavailable. Please try a different server.</p>
                            <button class="btn btn-primary" onclick="retryPlayer()">
                                <i class="fas fa-redo me-2"></i>Retry
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Episode Selector for TV Shows -->
                {% if media_type == 'tv' %}
                <div class="episode-selector">
                    <h5 class="server-title">
                        <i class="fas fa-list me-2"></i>Episode Selection
                    </h5>
                    <div class="episode-controls">
                        <label for="season-input" class="form-label me-2">Season:</label>
                        <input type="number" id="season-input" class="episode-input" 
                               value="{{ season }}" min="1" max="20">
                        
                        <label for="episode-input" class="form-label me-2 ms-3">Episode:</label>
                        <input type="number" id="episode-input" class="episode-input" 
                               value="{{ episode }}" min="1" max="50">
                        
                        <button class="btn btn-primary ms-3" onclick="changeEpisode()">
                            <i class="fas fa-play me-2"></i>Watch
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Server Selection -->
                <div class="server-selection">
                    <h5 class="server-title">
                        <i class="fas fa-server me-2"></i>Choose Server
                    </h5>
                    {% for server in servers %}
                    <a href="javascript:void(0)" 
                       class="server-btn {{ 'active' if server.id == current_server else '' }}"
                       onclick="changeServer('{{ server.id }}')">
                        <div class="server-name">{{ server.name }}</div>
                        <div class="server-desc">{{ server.description }}</div>
                    </a>
                    {% endfor %}
                </div>
                
                <!-- Content Info -->
                <div class="content-info">
                    <img src="https://image.tmdb.org/t/p/w300{{ content.poster_path }}" 
                         alt="{{ content.title if media_type == 'movie' else content.name }}"
                         class="content-poster"
                         onerror="this.src='https://via.placeholder.com/300x450/333/fff?text=No+Image'">
                    
                    <h5 class="content-title">
                        {{ content.title if media_type == 'movie' else content.name }}
                    </h5>
                    
                    <div class="content-meta">
                        <span>
                            <i class="fas fa-calendar me-1"></i>
                            {{ content.release_date[:4] if media_type == 'movie' and content.release_date 
                               else content.first_air_date[:4] if media_type == 'tv' and content.first_air_date 
                               else 'N/A' }}
                        </span>
                        <span>
                            <i class="fas fa-star text-warning me-1"></i>
                            {{ "%.1f"|format(content.vote_average) }}
                        </span>
                        {% if media_type == 'movie' and content.runtime %}
                        <span>
                            <i class="fas fa-clock me-1"></i>
                            {{ content.runtime }}m
                        </span>
                        {% endif %}
                    </div>
                    
                    {% if content.overview %}
                    <p class="content-overview">
                        {{ content.overview[:200] }}{% if content.overview|length > 200 %}...{% endif %}
                    </p>
                    {% endif %}
                </div>
                
                <!-- Debug Panel -->
                <div class="debug-panel">
                    <div class="debug-title">
                        <i class="fas fa-bug me-2"></i>Debug Info
                    </div>
                    <div class="debug-info">
                        <strong>Current Server:</strong> {{ current_server }}<br>
                        <strong>Media Type:</strong> {{ media_type }}<br>
                        <strong>TMDB ID:</strong> {{ tmdb_id }}<br>
                        {% if media_type == 'tv' %}
                        <strong>Season/Episode:</strong> {{ season }}/{{ episode }}<br>
                        {% endif %}
                        <strong>Stream URL:</strong><br>
                        <small>{{ streaming_urls[current_server] }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentServer = '{{ current_server }}';
let streamingUrls = {{ streaming_urls | tojson }};

// Hide loading overlay when iframe loads
document.getElementById('player-iframe').addEventListener('load', function() {
    document.getElementById('loading-overlay').style.display = 'none';
});

// Show error overlay if iframe fails to load
document.getElementById('player-iframe').addEventListener('error', function() {
    document.getElementById('loading-overlay').style.display = 'none';
    document.getElementById('error-overlay').style.display = 'flex';
});

function changeServer(serverId) {
    currentServer = serverId;
    
    // Update active server button
    document.querySelectorAll('.server-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.closest('.server-btn').classList.add('active');
    
    // Show loading overlay
    document.getElementById('loading-overlay').style.display = 'flex';
    document.getElementById('error-overlay').style.display = 'none';
    
    // Change iframe source
    const iframe = document.getElementById('player-iframe');
    iframe.src = streamingUrls[serverId];
    
    console.log('Switched to server:', serverId);
    console.log('New URL:', streamingUrls[serverId]);
}

function retryPlayer() {
    document.getElementById('error-overlay').style.display = 'none';
    document.getElementById('loading-overlay').style.display = 'flex';
    
    const iframe = document.getElementById('player-iframe');
    iframe.src = iframe.src; // Reload current source
}

{% if media_type == 'tv' %}
function changeEpisode() {
    const season = document.getElementById('season-input').value;
    const episode = document.getElementById('episode-input').value;
    
    if (season && episode) {
        const url = new URL(window.location);
        url.searchParams.set('season', season);
        url.searchParams.set('episode', episode);
        window.location.href = url.toString();
    }
}

// Allow Enter key to change episode
document.getElementById('season-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') changeEpisode();
});

document.getElementById('episode-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') changeEpisode();
});
{% endif %}

// Auto-hide loading overlay after 10 seconds
setTimeout(function() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay.style.display !== 'none') {
        loadingOverlay.style.display = 'none';
    }
}, 10000);
</script>
{% endblock %}
