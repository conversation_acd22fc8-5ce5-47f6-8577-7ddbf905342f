{% extends "base.html" %}

{% block title %}
{% if query %}Search: {{ query }} - CineStream{% else %}Search - CineStream{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .search-header {
        background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 100%);
        padding: 3rem 0 2rem;
        text-align: center;
    }
    
    .search-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--text-primary);
    }
    
    .search-subtitle {
        font-size: 1.1rem;
        color: var(--text-secondary);
        margin-bottom: 2rem;
    }
    
    .search-form-large {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .search-input-large {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid #333;
        border-radius: 25px;
        color: var(--text-primary);
        padding: 1rem 1.5rem;
        font-size: 1.1rem;
        width: 100%;
    }
    
    .search-input-large:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(229, 9, 20, 0.25);
        color: var(--text-primary);
        outline: none;
    }
    
    .search-input-large::placeholder {
        color: var(--text-muted);
    }
    
    .search-btn-large {
        background: var(--primary-color);
        border: 1px solid var(--primary-color);
        color: var(--text-primary);
        padding: 1rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        margin-left: 1rem;
    }
    
    .search-btn-large:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
        color: var(--text-primary);
    }
    
    .search-results-section {
        padding: 3rem 0;
    }
    
    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #333;
    }
    
    .results-count {
        font-size: 1.2rem;
        color: var(--text-primary);
        font-weight: 600;
    }
    
    .results-query {
        color: var(--primary-color);
        font-weight: 700;
    }
    
    .media-type-badge {
        background: var(--primary-color);
        color: var(--text-primary);
        padding: 0.2rem 0.6rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        z-index: 2;
    }
    
    .media-type-badge.tv {
        background: #2196F3;
    }
    
    .media-type-badge.movie {
        background: var(--primary-color);
    }
    
    .movie-card {
        position: relative;
    }
    
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--text-secondary);
    }
    
    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .search-suggestions {
        background: var(--dark-secondary);
        border-radius: 12px;
        padding: 2rem;
        margin-top: 2rem;
        border: 1px solid #333;
    }
    
    .suggestions-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
    }
    
    .suggestion-tags {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }
    
    .suggestion-tag {
        background: var(--dark-tertiary);
        color: var(--text-secondary);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border: 1px solid #333;
    }
    
    .suggestion-tag:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    @media (max-width: 768px) {
        .search-form-large {
            flex-direction: column;
            gap: 1rem;
        }
        
        .search-btn-large {
            margin-left: 0;
            width: 100%;
        }
        
        .results-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Search Header -->
<section class="search-header">
    <div class="container">
        <h1 class="search-title">
            <i class="fas fa-search me-3"></i>Search
        </h1>
        {% if query %}
        <p class="search-subtitle">Search results for "{{ query }}"</p>
        {% else %}
        <p class="search-subtitle">Find your favorite movies and TV shows</p>
        {% endif %}
        
        <!-- Large Search Form -->
        <form class="search-form-large d-flex" method="GET" action="{{ url_for('search') }}">
            <input type="text" name="q" class="search-input-large" 
                   placeholder="Search for movies, TV shows, actors..." 
                   value="{{ query }}" autofocus>
            <button class="btn search-btn-large" type="submit">
                <i class="fas fa-search me-2"></i>Search
            </button>
        </form>
    </div>
</section>

<!-- Search Results -->
<section class="search-results-section">
    <div class="container">
        {% if query %}
            {% if results %}
            <div class="results-header">
                <div class="results-count">
                    Found {{ results|length }} results for "<span class="results-query">{{ query }}</span>"
                </div>
                {% if total_pages > 1 %}
                <div class="pagination-info">
                    Page {{ current_page }} of {{ total_pages }}
                </div>
                {% endif %}
            </div>
            
            <div class="row g-4">
                {% for item in results %}
                <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                    <div class="movie-card">
                        <span class="media-type-badge {{ item.media_type }}">
                            {{ 'Movie' if item.media_type == 'movie' else 'TV Show' }}
                        </span>
                        
                        <a href="{% if item.media_type == 'movie' %}{{ url_for('movie_details', movie_id=item.id) }}{% else %}{{ url_for('tv_details', tv_id=item.id) }}{% endif %}" 
                           class="text-decoration-none">
                            <img src="{% if item.poster_path %}https://image.tmdb.org/t/p/w500{{ item.poster_path }}{% else %}https://via.placeholder.com/500x750/333/fff?text=No+Image{% endif %}" 
                                 alt="{{ item.title if item.media_type == 'movie' else item.name }}" 
                                 class="movie-poster">
                            <div class="movie-info">
                                <h5 class="movie-title">
                                    {{ item.title if item.media_type == 'movie' else item.name }}
                                </h5>
                                <div class="movie-meta">
                                    <span>
                                        {% if item.media_type == 'movie' and item.release_date %}
                                            {{ item.release_date[:4] }}
                                        {% elif item.media_type == 'tv' and item.first_air_date %}
                                            {{ item.first_air_date[:4] }}
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </span>
                                    <span class="rating">
                                        <i class="fas fa-star"></i> {{ "%.1f"|format(item.vote_average) }}
                                    </span>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if total_pages > 1 %}
            <div class="pagination-wrapper">
                <a href="{{ url_for('search', q=query, page=current_page-1) if current_page > 1 else '#' }}" 
                   class="pagination-btn {{ 'disabled' if current_page <= 1 else '' }}">
                    <i class="fas fa-chevron-left me-2"></i>Previous
                </a>
                
                <div class="pagination-info">
                    Page {{ current_page }} of {{ total_pages }}
                </div>
                
                <a href="{{ url_for('search', q=query, page=current_page+1) if current_page < total_pages else '#' }}" 
                   class="pagination-btn {{ 'disabled' if current_page >= total_pages else '' }}">
                    Next<i class="fas fa-chevron-right ms-2"></i>
                </a>
            </div>
            {% endif %}
            
            {% else %}
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No Results Found</h3>
                <p>Sorry, we couldn't find any movies or TV shows matching "{{ query }}".</p>
                <p>Try searching with different keywords or check your spelling.</p>
            </div>
            {% endif %}
        {% else %}
        <!-- Search Suggestions -->
        <div class="search-suggestions">
            <h3 class="suggestions-title">
                <i class="fas fa-lightbulb me-2"></i>Popular Searches
            </h3>
            <div class="suggestion-tags">
                <a href="{{ url_for('search', q='avengers') }}" class="suggestion-tag">Avengers</a>
                <a href="{{ url_for('search', q='batman') }}" class="suggestion-tag">Batman</a>
                <a href="{{ url_for('search', q='breaking bad') }}" class="suggestion-tag">Breaking Bad</a>
                <a href="{{ url_for('search', q='game of thrones') }}" class="suggestion-tag">Game of Thrones</a>
                <a href="{{ url_for('search', q='marvel') }}" class="suggestion-tag">Marvel</a>
                <a href="{{ url_for('search', q='star wars') }}" class="suggestion-tag">Star Wars</a>
                <a href="{{ url_for('search', q='stranger things') }}" class="suggestion-tag">Stranger Things</a>
                <a href="{{ url_for('search', q='the office') }}" class="suggestion-tag">The Office</a>
            </div>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Focus search input on page load
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input-large');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});

// Auto-submit search on Enter
document.querySelector('.search-input-large').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.closest('form').submit();
    }
});

// Smooth scrolling for pagination
document.querySelectorAll('.pagination-btn:not(.disabled)').forEach(btn => {
    btn.addEventListener('click', function(e) {
        if (this.href !== '#') {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });
});
</script>
{% endblock %}
