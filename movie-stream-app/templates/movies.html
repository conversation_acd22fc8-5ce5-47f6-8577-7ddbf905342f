{% extends "base.html" %}

{% block title %}Movies - CineStream{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 100%);
        padding: 3rem 0 2rem;
        text-align: center;
    }
    
    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, var(--primary-color), #ff1e2d);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
        color: var(--text-secondary);
        margin-bottom: 0;
    }
    
    .filters-section {
        background: var(--dark-secondary);
        padding: 2rem 0;
        border-bottom: 1px solid #333;
    }
    
    .filter-group {
        margin-bottom: 1.5rem;
    }
    
    .filter-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        display: block;
    }
    
    .filter-buttons {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }
    
    .filter-btn {
        background: var(--dark-tertiary);
        color: var(--text-secondary);
        border: 1px solid #333;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        white-space: nowrap;
    }
    
    .filter-btn:hover,
    .filter-btn.active {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    .movies-grid {
        padding: 3rem 0;
    }
    
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 3rem;
        padding: 2rem 0;
    }
    
    .pagination-btn {
        background: var(--dark-secondary);
        color: var(--text-secondary);
        border: 1px solid #333;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .pagination-btn:hover:not(.disabled) {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    .pagination-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }
    
    .pagination-info {
        background: var(--dark-secondary);
        color: var(--text-primary);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        border: 1px solid #333;
        font-weight: 600;
    }
    
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--text-secondary);
    }
    
    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .filter-buttons {
            justify-content: center;
        }
        
        .pagination-wrapper {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-film me-3"></i>Movies
        </h1>
        <p class="page-subtitle">Discover amazing movies from around the world</p>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-filter me-2"></i>Categories
                    </label>
                    <div class="filter-buttons">
                        <a href="{{ url_for('movies', category='popular') }}" 
                           class="filter-btn {{ 'active' if category == 'popular' else '' }}">
                            <i class="fas fa-fire me-1"></i>Popular
                        </a>
                        <a href="{{ url_for('movies', category='top_rated') }}" 
                           class="filter-btn {{ 'active' if category == 'top_rated' else '' }}">
                            <i class="fas fa-star me-1"></i>Top Rated
                        </a>
                        <a href="{{ url_for('movies', category='upcoming') }}" 
                           class="filter-btn {{ 'active' if category == 'upcoming' else '' }}">
                            <i class="fas fa-calendar me-1"></i>Upcoming
                        </a>
                        <a href="{{ url_for('movies', category='now_playing') }}" 
                           class="filter-btn {{ 'active' if category == 'now_playing' else '' }}">
                            <i class="fas fa-play me-1"></i>Now Playing
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Movies Grid -->
<section class="movies-grid">
    <div class="container">
        {% if movies %}
        <div class="row g-4">
            {% for movie in movies %}
            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                <div class="movie-card">
                    <a href="{{ url_for('movie_details', movie_id=movie.id) }}" class="text-decoration-none">
                        <img src="https://image.tmdb.org/t/p/w500{{ movie.poster_path }}" 
                             alt="{{ movie.title }}" 
                             class="movie-poster"
                             onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                        <div class="movie-info">
                            <h5 class="movie-title">{{ movie.title }}</h5>
                            <div class="movie-meta">
                                <span>{{ movie.release_date[:4] if movie.release_date else 'N/A' }}</span>
                                <span class="rating">
                                    <i class="fas fa-star"></i> {{ "%.1f"|format(movie.vote_average) }}
                                </span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        <div class="pagination-wrapper">
            <a href="{{ url_for('movies', category=category, page=current_page-1) if current_page > 1 else '#' }}" 
               class="pagination-btn {{ 'disabled' if current_page <= 1 else '' }}">
                <i class="fas fa-chevron-left me-2"></i>Previous
            </a>
            
            <div class="pagination-info">
                Page {{ current_page }} of {{ total_pages }}
            </div>
            
            <a href="{{ url_for('movies', category=category, page=current_page+1) if current_page < total_pages else '#' }}" 
               class="pagination-btn {{ 'disabled' if current_page >= total_pages else '' }}">
                Next<i class="fas fa-chevron-right ms-2"></i>
            </a>
        </div>
        
        {% else %}
        <div class="no-results">
            <i class="fas fa-film"></i>
            <h3>No Movies Found</h3>
            <p>Sorry, we couldn't find any movies in this category.</p>
            <a href="{{ url_for('movies', category='popular') }}" class="btn btn-primary">
                <i class="fas fa-fire me-2"></i>Browse Popular Movies
            </a>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Add active class to Movies nav item
document.addEventListener('DOMContentLoaded', function() {
    const moviesLink = document.querySelector('a[href="{{ url_for("movies") }}"]');
    if (moviesLink) {
        moviesLink.classList.add('active');
    }
});

// Smooth scrolling for pagination
document.querySelectorAll('.pagination-btn:not(.disabled)').forEach(btn => {
    btn.addEventListener('click', function(e) {
        if (this.href !== '#') {
            // Scroll to top smoothly when changing pages
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });
});

// Keyboard navigation for movie cards
document.querySelectorAll('.movie-card a').forEach(link => {
    link.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
        }
    });
});
</script>
{% endblock %}
