{% extends "base.html" %}

{% block title %}TV Shows - CineStream{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--dark-bg) 0%, var(--dark-secondary) 100%);
        padding: 3rem 0 2rem;
        text-align: center;
    }
    
    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #2196F3, #21CBF3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .page-subtitle {
        font-size: 1.1rem;
        color: var(--text-secondary);
        margin-bottom: 0;
    }
    
    .filters-section {
        background: var(--dark-secondary);
        padding: 2rem 0;
        border-bottom: 1px solid #333;
    }
    
    .filter-group {
        margin-bottom: 1.5rem;
    }
    
    .filter-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        display: block;
    }
    
    .filter-buttons {
        display: flex;
        gap: 0.75rem;
        flex-wrap: wrap;
    }
    
    .filter-btn {
        background: var(--dark-tertiary);
        color: var(--text-secondary);
        border: 1px solid #333;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        white-space: nowrap;
    }
    
    .filter-btn:hover,
    .filter-btn.active {
        background: #2196F3;
        border-color: #2196F3;
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    .shows-grid {
        padding: 3rem 0;
    }
    
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 3rem;
        padding: 2rem 0;
    }
    
    .pagination-btn {
        background: var(--dark-secondary);
        color: var(--text-secondary);
        border: 1px solid #333;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .pagination-btn:hover:not(.disabled) {
        background: #2196F3;
        border-color: #2196F3;
        color: var(--text-primary);
        transform: translateY(-2px);
    }
    
    .pagination-btn.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        pointer-events: none;
    }
    
    .pagination-info {
        background: var(--dark-secondary);
        color: var(--text-primary);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        border: 1px solid #333;
        font-weight: 600;
    }
    
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--text-secondary);
    }
    
    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .filter-buttons {
            justify-content: center;
        }
        
        .pagination-wrapper {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-tv me-3"></i>TV Shows
        </h1>
        <p class="page-subtitle">Discover amazing TV shows and series from around the world</p>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="filter-group">
                    <label class="filter-label">
                        <i class="fas fa-filter me-2"></i>Categories
                    </label>
                    <div class="filter-buttons">
                        <a href="{{ url_for('tv_shows', category='popular') }}" 
                           class="filter-btn {{ 'active' if category == 'popular' else '' }}">
                            <i class="fas fa-fire me-1"></i>Popular
                        </a>
                        <a href="{{ url_for('tv_shows', category='top_rated') }}" 
                           class="filter-btn {{ 'active' if category == 'top_rated' else '' }}">
                            <i class="fas fa-star me-1"></i>Top Rated
                        </a>
                        <a href="{{ url_for('tv_shows', category='on_the_air') }}" 
                           class="filter-btn {{ 'active' if category == 'on_the_air' else '' }}">
                            <i class="fas fa-broadcast-tower me-1"></i>On The Air
                        </a>
                        <a href="{{ url_for('tv_shows', category='airing_today') }}" 
                           class="filter-btn {{ 'active' if category == 'airing_today' else '' }}">
                            <i class="fas fa-calendar-day me-1"></i>Airing Today
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- TV Shows Grid -->
<section class="shows-grid">
    <div class="container">
        {% if shows %}
        <div class="row g-4">
            {% for show in shows %}
            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                <div class="movie-card">
                    <a href="{{ url_for('tv_details', tv_id=show.id) }}" class="text-decoration-none">
                        <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}" 
                             alt="{{ show.name }}" 
                             class="movie-poster"
                             onerror="this.src='https://via.placeholder.com/500x750/333/fff?text=No+Image'">
                        <div class="movie-info">
                            <h5 class="movie-title">{{ show.name }}</h5>
                            <div class="movie-meta">
                                <span>{{ show.first_air_date[:4] if show.first_air_date else 'N/A' }}</span>
                                <span class="rating">
                                    <i class="fas fa-star"></i> {{ "%.1f"|format(show.vote_average) }}
                                </span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        <div class="pagination-wrapper">
            <a href="{{ url_for('tv_shows', category=category, page=current_page-1) if current_page > 1 else '#' }}" 
               class="pagination-btn {{ 'disabled' if current_page <= 1 else '' }}">
                <i class="fas fa-chevron-left me-2"></i>Previous
            </a>
            
            <div class="pagination-info">
                Page {{ current_page }} of {{ total_pages }}
            </div>
            
            <a href="{{ url_for('tv_shows', category=category, page=current_page+1) if current_page < total_pages else '#' }}" 
               class="pagination-btn {{ 'disabled' if current_page >= total_pages else '' }}">
                Next<i class="fas fa-chevron-right ms-2"></i>
            </a>
        </div>
        
        {% else %}
        <div class="no-results">
            <i class="fas fa-tv"></i>
            <h3>No TV Shows Found</h3>
            <p>Sorry, we couldn't find any TV shows in this category.</p>
            <a href="{{ url_for('tv_shows', category='popular') }}" class="btn btn-primary">
                <i class="fas fa-fire me-2"></i>Browse Popular Shows
            </a>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Add active class to TV Shows nav item
document.addEventListener('DOMContentLoaded', function() {
    const tvLink = document.querySelector('a[href="{{ url_for("tv_shows") }}"]');
    if (tvLink) {
        tvLink.classList.add('active');
    }
});

// Smooth scrolling for pagination
document.querySelectorAll('.pagination-btn:not(.disabled)').forEach(btn => {
    btn.addEventListener('click', function(e) {
        if (this.href !== '#') {
            // Scroll to top smoothly when changing pages
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    });
});

// Keyboard navigation for show cards
document.querySelectorAll('.movie-card a').forEach(link => {
    link.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
        }
    });
});
</script>
{% endblock %}
