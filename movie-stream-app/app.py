#!/usr/bin/env python3
"""
CineStream - Professional Movie Streaming Platform
Built with Flask, featuring TMDB integration and multiple streaming sources
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
import requests
import os
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'

# TMDB API Configuration
TMDB_API_KEY = '3308647fabe47a844ab269e6eab19132'
TMDB_BASE_URL = 'https://api.themoviedb.org/3'
TMDB_IMAGE_BASE = 'https://image.tmdb.org/t/p'

# Streaming servers configuration
STREAMING_SERVERS = [
    {
        'id': 'vidsrc_pro',
        'name': 'VidSrc Pro',
        'description': 'High quality streaming - Most reliable',
        'base_url': 'https://vidsrc.pro/embed'
    },
    {
        'id': 'vidsrc_to',
        'name': 'VidSrc TO',
        'description': 'Fast streaming with multiple sources',
        'base_url': 'https://vidsrc.to/embed'
    },
    {
        'id': 'embed_su',
        'name': 'Embed SU',
        'description': 'Reliable backup server',
        'base_url': 'https://embed.su/embed'
    },
    {
        'id': 'multiembed',
        'name': 'MultiEmbed',
        'description': 'Multiple streaming sources',
        'base_url': 'https://multiembed.mov/directstream.php'
    }
]

def make_tmdb_request(endpoint, params=None):
    """Make a request to TMDB API with error handling"""
    try:
        if params is None:
            params = {}
        params['api_key'] = TMDB_API_KEY
        
        response = requests.get(f"{TMDB_BASE_URL}/{endpoint}", params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"TMDB API error: {e}")
        return None

def get_streaming_url(server_id, media_type, tmdb_id, season=None, episode=None):
    """Generate streaming URL for given server and content"""
    server = next((s for s in STREAMING_SERVERS if s['id'] == server_id), None)
    if not server:
        return None
    
    base_url = server['base_url']
    
    if media_type == 'tv' and season and episode:
        if server_id == 'multiembed':
            return f"{base_url}?video_id={tmdb_id}&tmdb=1&s={season}&e={episode}"
        else:
            return f"{base_url}/tv/{tmdb_id}/{season}/{episode}"
    else:
        if server_id == 'multiembed':
            return f"{base_url}?video_id={tmdb_id}&tmdb=1"
        else:
            return f"{base_url}/movie/{tmdb_id}"

@app.route('/')
def home():
    """Home page with trending movies and TV shows"""
    try:
        # Get trending movies and TV shows
        trending_movies = make_tmdb_request('trending/movie/week')
        trending_tv = make_tmdb_request('trending/tv/week')
        popular_movies = make_tmdb_request('movie/popular')
        
        return render_template('home.html', 
                             trending_movies=trending_movies.get('results', [])[:10] if trending_movies else [],
                             trending_tv=trending_tv.get('results', [])[:10] if trending_tv else [],
                             popular_movies=popular_movies.get('results', [])[:10] if popular_movies else [])
    except Exception as e:
        logger.error(f"Home page error: {e}")
        return render_template('error.html', error="Failed to load content"), 500

@app.route('/movies')
def movies():
    """Movies page with filtering and pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        category = request.args.get('category', 'popular')
        
        valid_categories = ['popular', 'top_rated', 'upcoming', 'now_playing']
        if category not in valid_categories:
            category = 'popular'
        
        movies_data = make_tmdb_request(f'movie/{category}', {'page': page})
        genres_data = make_tmdb_request('genre/movie/list')
        
        return render_template('movies.html',
                             movies=movies_data.get('results', []) if movies_data else [],
                             current_page=page,
                             total_pages=min(movies_data.get('total_pages', 1), 500) if movies_data else 1,
                             category=category,
                             genres=genres_data.get('genres', []) if genres_data else [])
    except Exception as e:
        logger.error(f"Movies page error: {e}")
        return render_template('error.html', error="Failed to load movies"), 500

@app.route('/tv')
def tv_shows():
    """TV shows page with filtering and pagination"""
    try:
        page = request.args.get('page', 1, type=int)
        category = request.args.get('category', 'popular')
        
        valid_categories = ['popular', 'top_rated', 'on_the_air', 'airing_today']
        if category not in valid_categories:
            category = 'popular'
        
        tv_data = make_tmdb_request(f'tv/{category}', {'page': page})
        genres_data = make_tmdb_request('genre/tv/list')
        
        return render_template('tv.html',
                             shows=tv_data.get('results', []) if tv_data else [],
                             current_page=page,
                             total_pages=min(tv_data.get('total_pages', 1), 500) if tv_data else 1,
                             category=category,
                             genres=genres_data.get('genres', []) if genres_data else [])
    except Exception as e:
        logger.error(f"TV shows page error: {e}")
        return render_template('error.html', error="Failed to load TV shows"), 500

@app.route('/movie/<int:movie_id>')
def movie_details(movie_id):
    """Movie details page"""
    try:
        movie = make_tmdb_request(f'movie/{movie_id}')
        credits = make_tmdb_request(f'movie/{movie_id}/credits')
        similar = make_tmdb_request(f'movie/{movie_id}/similar')
        
        if not movie:
            return render_template('error.html', error="Movie not found"), 404
        
        return render_template('movie_details.html',
                             movie=movie,
                             cast=credits.get('cast', [])[:12] if credits else [],
                             similar=similar.get('results', [])[:8] if similar else [],
                             servers=STREAMING_SERVERS)
    except Exception as e:
        logger.error(f"Movie details error: {e}")
        return render_template('error.html', error="Failed to load movie details"), 500

@app.route('/tv/<int:tv_id>')
def tv_details(tv_id):
    """TV show details page"""
    try:
        show = make_tmdb_request(f'tv/{tv_id}')
        credits = make_tmdb_request(f'tv/{tv_id}/credits')
        similar = make_tmdb_request(f'tv/{tv_id}/similar')
        
        if not show:
            return render_template('error.html', error="TV show not found"), 404
        
        return render_template('tv_details.html',
                             show=show,
                             cast=credits.get('cast', [])[:12] if credits else [],
                             similar=similar.get('results', [])[:8] if similar else [],
                             servers=STREAMING_SERVERS)
    except Exception as e:
        logger.error(f"TV details error: {e}")
        return render_template('error.html', error="Failed to load TV show details"), 500

@app.route('/watch/<media_type>/<int:tmdb_id>')
def watch(media_type, tmdb_id):
    """Watch page for movies and TV shows"""
    try:
        season = request.args.get('season', 1, type=int)
        episode = request.args.get('episode', 1, type=int)
        server = request.args.get('server', STREAMING_SERVERS[0]['id'])
        
        if media_type not in ['movie', 'tv']:
            return redirect(url_for('home'))
        
        # Get content details
        content = make_tmdb_request(f'{media_type}/{tmdb_id}')
        if not content:
            return render_template('error.html', error="Content not found"), 404
        
        # Generate streaming URLs for all servers
        streaming_urls = {}
        for srv in STREAMING_SERVERS:
            streaming_urls[srv['id']] = get_streaming_url(
                srv['id'], media_type, tmdb_id, season, episode
            )
        
        return render_template('watch.html',
                             content=content,
                             media_type=media_type,
                             tmdb_id=tmdb_id,
                             season=season,
                             episode=episode,
                             current_server=server,
                             servers=STREAMING_SERVERS,
                             streaming_urls=streaming_urls)
    except Exception as e:
        logger.error(f"Watch page error: {e}")
        return render_template('error.html', error="Failed to load player"), 500

@app.route('/search')
def search():
    """Search functionality"""
    try:
        query = request.args.get('q', '').strip()
        page = request.args.get('page', 1, type=int)
        
        if not query:
            return render_template('search.html', results=[], query='', total_pages=0)
        
        search_data = make_tmdb_request('search/multi', {
            'query': query,
            'page': page
        })
        
        # Filter only movies and TV shows
        results = []
        if search_data and 'results' in search_data:
            results = [item for item in search_data['results'] 
                      if item.get('media_type') in ['movie', 'tv']]
        
        return render_template('search.html',
                             results=results,
                             query=query,
                             current_page=page,
                             total_pages=min(search_data.get('total_pages', 1), 500) if search_data else 1)
    except Exception as e:
        logger.error(f"Search error: {e}")
        return render_template('error.html', error="Search failed"), 500

@app.route('/api/streaming-url')
def api_streaming_url():
    """API endpoint to get streaming URL"""
    try:
        server_id = request.args.get('server')
        media_type = request.args.get('type')
        tmdb_id = request.args.get('id')
        season = request.args.get('season')
        episode = request.args.get('episode')
        
        if not all([server_id, media_type, tmdb_id]):
            return jsonify({'error': 'Missing required parameters'}), 400
        
        url = get_streaming_url(server_id, media_type, tmdb_id, season, episode)
        
        if url:
            return jsonify({'url': url, 'success': True})
        else:
            return jsonify({'error': 'Invalid server or parameters'}), 400
    except Exception as e:
        logger.error(f"API error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error="Internal server error"), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
